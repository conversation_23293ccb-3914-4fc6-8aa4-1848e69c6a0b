<?php

declare(strict_types=1);

namespace DrxDion\Interfaces;

use DrxDion\Models\Settings;

/**
 * Settings Repository Interface
 * Defines methods for settings storage operations
 */
interface SettingsRepositoryInterface
{
    /**
     * Get all settings as key-value pairs
     */
    public function getAll(): array;

    /**
     * Get a specific setting by key
     */
    public function get(string $key): ?Settings;

    /**
     * Set a setting value
     */
    public function set(string $key, string $value, string $type = 'string', string $description = ''): bool;

    /**
     * Update multiple settings at once
     */
    public function updateMultiple(array $settings): bool;

    /**
     * Delete a setting
     */
    public function delete(string $key): bool;

    /**
     * Check if a setting exists
     */
    public function exists(string $key): bool;

    /**
     * Get settings by category
     */
    public function getByCategory(string $category): array;
}
