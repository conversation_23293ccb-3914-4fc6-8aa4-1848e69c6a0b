<?php

declare(strict_types=1);

namespace DrxDion\Config;

use Closure;
use InvalidArgumentException;
use ReflectionClass;
use ReflectionException;
use ReflectionParameter;

/**
 * Dependency Injection Container
 * Manages service registration and resolution
 */
final class Container
{
    private array $services = [];
    private array $instances = [];
    private array $singletons = [];

    public function bind(string $abstract, Closure|string|null $concrete = null, bool $singleton = false): void
    {
        if ($concrete === null) {
            $concrete = $abstract;
        }

        $this->services[$abstract] = [
            'concrete' => $concrete,
            'singleton' => $singleton,
        ];
    }

    public function singleton(string $abstract, Closure|string|null $concrete = null): void
    {
        $this->bind($abstract, $concrete, true);
    }

    public function instance(string $abstract, object $instance): void
    {
        $this->instances[$abstract] = $instance;
    }

    public function get(string $abstract): mixed
    {
        // Return existing instance if it's a singleton
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        // Check if service is registered
        if (!isset($this->services[$abstract])) {
            return $this->resolve($abstract);
        }

        $service = $this->services[$abstract];
        $concrete = $service['concrete'];

        if ($concrete instanceof Closure) {
            $object = $concrete($this);
        } elseif (is_string($concrete)) {
            $object = $this->resolve($concrete);
        } else {
            throw new InvalidArgumentException("Invalid service definition for {$abstract}");
        }

        // Store singleton instances
        if ($service['singleton']) {
            $this->instances[$abstract] = $object;
        }

        return $object;
    }

    public function has(string $abstract): bool
    {
        return isset($this->services[$abstract]) || isset($this->instances[$abstract]);
    }

    private function resolve(string $class): object
    {
        try {
            $reflectionClass = new ReflectionClass($class);
        } catch (ReflectionException $e) {
            throw new InvalidArgumentException("Class {$class} does not exist", 0, $e);
        }

        if (!$reflectionClass->isInstantiable()) {
            throw new InvalidArgumentException("Class {$class} is not instantiable");
        }

        $constructor = $reflectionClass->getConstructor();

        if ($constructor === null) {
            return new $class();
        }

        $parameters = $constructor->getParameters();
        $dependencies = [];

        foreach ($parameters as $parameter) {
            $dependencies[] = $this->resolveDependency($parameter);
        }

        return $reflectionClass->newInstanceArgs($dependencies);
    }

    private function resolveDependency(ReflectionParameter $parameter): mixed
    {
        $type = $parameter->getType();

        if ($type === null) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }

            throw new InvalidArgumentException(
                "Cannot resolve parameter {$parameter->getName()} without type hint"
            );
        }

        $typeName = $type->getName();

        if ($type->isBuiltin()) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }

            throw new InvalidArgumentException(
                "Cannot resolve built-in type {$typeName} for parameter {$parameter->getName()}"
            );
        }

        try {
            return $this->get($typeName);
        } catch (InvalidArgumentException $e) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }

            if ($type->allowsNull()) {
                return null;
            }

            throw $e;
        }
    }

    public function call(callable $callback, array $parameters = []): mixed
    {
        if (is_array($callback)) {
            [$class, $method] = $callback;

            if (is_string($class)) {
                $class = $this->get($class);
            }

            $callback = [$class, $method];
        }

        return call_user_func_array($callback, $parameters);
    }
}
