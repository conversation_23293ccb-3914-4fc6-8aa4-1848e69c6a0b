<?php

declare(strict_types=1);

namespace App\Http\Controllers;

/**
 * Base Controller
 * Laravel-style base controller for DrxDion
 */
abstract class Controller
{
    protected function jsonResponse(array $data, int $status = 200): void
    {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
    }

    protected function errorResponse(string $message, int $status = 400): void
    {
        $this->jsonResponse([
            'success' => false,
            'error' => $message
        ], $status);
    }

    protected function successResponse(array $data = [], string $message = 'Success'): void
    {
        $this->jsonResponse([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }
}
