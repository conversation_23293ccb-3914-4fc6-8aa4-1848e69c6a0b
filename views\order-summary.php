<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Order Summary - DrxDion</title>
    <link href="/assets/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="/assets/css/bootstrap-icons.css" />
    <style>
        body {
            background-color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI",
                Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .order-summary-wrapper {
            max-width: 500px;
            margin: 2rem auto;
            padding: 1rem;
        }

        .status-icon {
            width: 48px;
            height: 48px;
            background-color: #1bb55c;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }

        .status-icon i {
            color: white;
            font-size: 1.5rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #666;
        }

        .info-value {
            color: #000;
            font-weight: 500;
            text-align: right;
        }

        .btn-back {
            background-color: #0d6efd;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.75rem 2rem;
            width: 100%;
            margin-top: 1.5rem;
        }

        .btn-back:hover {
            background-color: #0b5ed7;
        }

        .thank-you-text {
            color: #666;
            font-size: 0.9rem;
            text-align: center;
            margin: 1.5rem 0;
        }
    </style>
</head>

<body>
    <div class="order-summary-wrapper">
        <?php if ($status === 'success'): ?>
            <div class="text-center mb-4">
                <div class="status-icon">
                    <i class="bi bi-check-lg"></i>
                </div>
                <h4>Order Completed Successfully!</h4>
            </div>
            <div class="order-info">
                <div class="info-row">
                    <span class="info-label">Order ID:</span>
                    <span class="info-value"><?= htmlspecialchars($data['orderId'] ?? '') ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Amount:</span>
                    <span class="info-value">$<?= htmlspecialchars(number_format($data['amount'] ?? 0, 2)) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Payment Method:</span>
                    <span class="info-value"><?= htmlspecialchars($data['paymentMethod'] ?? '') ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Shipping Address:</span>
                    <span class="info-value"><?= htmlspecialchars($data['shippingAddress'] ?? '') ?></span>
                </div>
            </div>
            <p class="thank-you-text">
                Thank you for your order! We'll process it right away and send
                you a confirmation email.
            </p>
        <?php else: ?>
            <div class="text-center mb-4">
                <div class="status-icon bg-danger">
                    <i class="bi bi-x-lg"></i>
                </div>
                <h4>Order Processing Failed</h4>
            </div>
            <p class="text-center text-muted mb-4"><?= htmlspecialchars($message ?? '') ?></p>
        <?php endif; ?>
        <a href="/" class="btn btn-back">Back to Home</a>
    </div>

    <script src="/assets/js/bootstrap.bundle.min.js"></script>
</body>

</html>