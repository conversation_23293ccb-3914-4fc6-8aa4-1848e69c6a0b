<?php

declare(strict_types=1);

/**
 * API Routes
 * DrxDion E-commerce Platform
 */

return [
    // API routes
    '/api/csrf-token' => ['GET', 'App\Http\Controllers\ApiController@csrfToken'],
    '/api/get-settings' => ['GET', 'App\Http\Controllers\ApiController@getSettings'],
    '/api/settings/update' => ['POST', 'App\Http\Controllers\ApiController@updateSettings'],
    '/api/orders' => ['GET', 'App\Http\Controllers\ApiController@getOrders'],
    '/api/contact' => ['POST', 'App\Http\Controllers\ApiController@contact'],

    // Admin API routes
    '/admin/login' => ['POST', 'App\Http\Controllers\AdminController@login'],
    '/admin/logout' => ['POST', 'App\Http\Controllers\AdminController@logout'],
    '/admin/logout' => ['GET', 'App\Http\Controllers\AdminController@logout'],
    '/api/admin/storage/status' => ['GET', 'App\Http\Controllers\ApiController@getStorageStatus'],
    '/api/admin/storage/sync' => ['POST', 'App\Http\Controllers\ApiController@syncStorage'],
];
