<!DOCTYPE html>
<html lang="tr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DrxDion - Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .debug-info {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .link {
            color: #2c6fb0;
            text-decoration: none;
        }

        .link:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>DrxDion - Debug Test Sayfası</h1>

        <div class="debug-info">
            <h3>Test Linkleri:</h3>
            <ul>
                <li><a href="/" class="link">Ana Sayfa (/)</a></li>
                <li><a href="/admin" class="link">Admin Panel (/admin)</a></li>
                <li><a href="/admin/login" class="link">Admin Login (/admin/login)</a></li>
                <li><a href="/admin/orders" class="link">Admin Orders (/admin/orders)</a></li>
                <li><a href="/admin/settings" class="link">Admin Settings (/admin/settings)</a></li>
            </ul>
        </div>

        <div class="debug-info">
            <h3>API Test Linkleri:</h3>
            <ul>
                <li><a href="/api/get-settings" class="link">Settings API (/api/get-settings)</a></li>
                <li><a href="/api/orders" class="link">Orders API (/api/orders)</a></li>
                <li><a href="/api/csrf-token" class="link">CSRF Token API (/api/csrf-token)</a></li>
            </ul>
        </div>

        <div class="debug-info">
            <h3>Static File Test:</h3>
            <p>CSS Test:
                <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
                <span id="css-test" style="color: red;">CSS YÜKLENMEDİ</span>
            </p>
            <script>
                // Test if CSS is loaded
                setTimeout(() => {
                    const testEl = document.getElementById('css-test');
                    if (getComputedStyle(testEl).color === 'rgb(0, 128, 0)') {
                        testEl.innerText = 'CSS YÜKLENDİ';
                        testEl.style.color = 'green';
                    }
                }, 100);
            </script>
        </div>

        <div class="debug-info">
            <h3>PHP Info:</h3>
            <p>PHP Version: <?php echo PHP_VERSION; ?></p>
            <p>Current Time: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>Request URI: <?php echo $_SERVER['REQUEST_URI'] ?? 'Not Set'; ?></p>
            <p>Request Method: <?php echo $_SERVER['REQUEST_METHOD'] ?? 'Not Set'; ?></p>
        </div>
    </div>
</body>

</html>