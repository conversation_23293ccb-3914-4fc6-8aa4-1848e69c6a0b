const sqlite3 = require("sqlite3").verbose();
const bcrypt = require("bcryptjs");
const path = require("path");

class Database {
	constructor() {
		// SQLite veritabanı dosyasının yolu
		const dbPath = path.join(__dirname, "orders.db");
		this.db = new sqlite3.Database(dbPath, (err) => {
			if (err) {
				console.error("Error opening database:", err);
				throw err;
			}
			console.log("Connected to SQLite database.");
		});

		// Veritabanını başlat
		this.initDatabase();
	}

	async initDatabase() {
		console.log("Initializing SQLite database...");
		// Orders tablosunu oluştur
		const createOrdersTable = `
			CREATE TABLE IF NOT EXISTS orders (
				id TEXT PRIMARY KEY,
				product_name TEXT NOT NULL,
				price REAL NOT NULL,
				total_price REAL NOT NULL,
				shipping_cost REAL DEFAULT 0,
				payment_method TEXT NOT NULL,
				payment_status TEXT DEFAULT 'pending',
				payment_id TEXT,
				customer_name TEXT,
				customer_email TEXT,
				shipping_address TEXT NOT NULL,
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
				updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
			)
		`;

		// Admin users tablosunu oluştur
		const createAdminUsersTable = `
			CREATE TABLE IF NOT EXISTS admin_users (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				username TEXT NOT NULL UNIQUE,
				password TEXT NOT NULL,
				email TEXT NOT NULL,
				created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
				updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
			)
		`;

		// Tabloları oluştur
		this.db.serialize(() => {
			this.db.run(createOrdersTable, (err) => {
				if (err) {
					console.error("Error creating orders table:", err);
				} else {
					console.log("Orders table ready.");
				}
			});

			this.db.run(createAdminUsersTable, (err) => {
				if (err) {
					console.error("Error creating admin_users table:", err);
				} else {
					console.log("Admin users table ready.");
					// Varsayılan admin kullanıcısını oluştur
					this.initializeDefaultAdmin();
				}
			});
		});
	}
	// Varsayılan admin kullanıcısını oluştur
	initializeDefaultAdmin() {
		const checkAdmin = `SELECT * FROM admin_users LIMIT 1`;
		this.db.get(checkAdmin, [], async (err, row) => {
			if (err) {
				console.error("Error checking admin user:", err);
				return;
			}
			if (!row) {
				// Admin yoksa güvenli default şifre oluştur
				console.log("Creating default admin user...");

				const adminUsername = "admin";
				const adminPassword = this.generateSecurePassword();

				console.warn(
					"⚠️  WARNING: Creating admin user with generated password!"
				);
				console.warn(`⚠️  Generated admin password: ${adminPassword}`);
				console.warn(
					"⚠️  Please change this password after first login!"
				);

				const hashedPassword = await bcrypt.hash(adminPassword, 12);
				const insertAdmin = `
					INSERT INTO admin_users (username, password, email)
					VALUES (?, ?, ?)
				`;

				this.db.run(
					insertAdmin,
					[adminUsername, hashedPassword, "<EMAIL>"],
					(err) => {
						if (err) {
							console.error("Error creating default admin:", err);
						} else {
							console.log(
								`✓ Admin user created (username: ${adminUsername})`
							);
							console.log(
								`✓ Generated password: ${adminPassword}`
							);
						}
					}
				);
			} else {
				console.log("✓ Admin user already exists");
			}
		});
	}

	// Güvenli şifre oluştur
	generateSecurePassword() {
		const crypto = require("crypto");
		const chars =
			"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
		let password = "";

		// En az 1 büyük harf, 1 küçük harf, 1 sayı, 1 özel karakter garantile
		password += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[crypto.randomInt(26)];
		password += "abcdefghijklmnopqrstuvwxyz"[crypto.randomInt(26)];
		password += "0123456789"[crypto.randomInt(10)];
		password += "!@#$%^&*"[crypto.randomInt(8)];

		// Kalan karakterleri rastgele ekle
		for (let i = 4; i < 16; i++) {
			password += chars[crypto.randomInt(chars.length)];
		}
		// Karakterleri karıştır
		return password
			.split("")
			.sort(() => 0.5 - Math.random())
			.join("");
	}

	// Sipariş oluştur
	async createOrder(orderData) {
		return new Promise((resolve, reject) => {
			console.log("Creating order with data:", orderData); // Debug log

			const query = `
				INSERT INTO orders (id, product_name, price, total_price, shipping_cost, payment_method, payment_status, customer_name, customer_email, shipping_address)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`;

			this.db.run(
				query,
				[
					orderData.orderId || orderData.id, // Handle both orderId and id
					orderData.productName || orderData.product_name, // Handle both productName and product_name
					orderData.price,
					orderData.totalPrice || orderData.total_price, // Handle both totalPrice and total_price
					orderData.shippingCost || orderData.shipping_cost || 0, // Handle both shippingCost and shipping_cost
					orderData.paymentMethod || orderData.payment_method, // Handle both paymentMethod and payment_method
					orderData.payment_status || "pending",
					orderData.customerName || orderData.customer_name, // Handle both customerName and customer_name
					orderData.customerEmail || orderData.customer_email, // Handle both customerEmail and customer_email
					orderData.shippingAddress || orderData.shipping_address, // Handle both shippingAddress and shipping_address
				],
				function (err) {
					if (err) {
						console.error("Error creating order:", err);
						reject(err);
					} else {
						console.log(
							"Order created with ID:",
							orderData.orderId || orderData.id
						);
						resolve({
							id: orderData.orderId || orderData.id,
							...orderData,
						});
					}
				}
			);
		});
	}

	// Sipariş durumunu güncelle
	async updateOrderStatus(orderId, status) {
		return new Promise((resolve, reject) => {
			const query = `UPDATE orders SET payment_status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;

			this.db.run(query, [status, orderId], function (err) {
				if (err) {
					console.error("Error updating order status:", err);
					reject(err);
				} else {
					console.log("Order status updated:", orderId, "->", status);
					resolve({ changes: this.changes });
				}
			});
		});
	}

	// Sipariş ödeme bilgilerini güncelle
	async updateOrderPayment(orderId, paymentId, status) {
		return new Promise((resolve, reject) => {
			const query = `UPDATE orders SET payment_id = ?, payment_status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;

			this.db.run(query, [paymentId, status, orderId], function (err) {
				if (err) {
					console.error("Error updating order payment:", err);
					reject(err);
				} else {
					console.log("Order payment updated:", orderId);
					resolve({ changes: this.changes });
				}
			});
		});
	}

	// Sipariş getir
	async getOrder(orderId) {
		return new Promise((resolve, reject) => {
			const query = `SELECT * FROM orders WHERE id = ?`;

			this.db.get(query, [orderId], (err, row) => {
				if (err) {
					console.error("Error getting order:", err);
					reject(err);
				} else {
					resolve(row);
				}
			});
		});
	}

	// Tüm siparişleri getir
	async getAllOrders() {
		return new Promise((resolve, reject) => {
			const query = `SELECT * FROM orders ORDER BY created_at DESC`;

			this.db.all(query, [], (err, rows) => {
				if (err) {
					console.error("Error getting all orders:", err);
					reject(err);
				} else {
					resolve(rows);
				}
			});
		});
	}

	// Siparişleri ara ve sayfalama
	async searchOrders(searchTerm = "", page = 1, limit = 10) {
		return new Promise((resolve, reject) => {
			const offset = (page - 1) * limit;

			let whereClause = "";
			let params = [];

			if (searchTerm) {
				whereClause = `WHERE product_name LIKE ? OR customer_name LIKE ? OR customer_email LIKE ? OR id LIKE ?`;
				const searchPattern = `%${searchTerm}%`;
				params = [
					searchPattern,
					searchPattern,
					searchPattern,
					searchPattern,
				];
			}

			// Toplam kayıt sayısını al
			const countQuery = `SELECT COUNT(*) as total FROM orders ${whereClause}`;

			this.db.get(countQuery, params, (err, countResult) => {
				if (err) {
					console.error("Error counting orders:", err);
					reject(err);
					return;
				}

				const total = countResult.total;
				const totalPages = Math.ceil(total / limit);

				// Sayfalanmış veriyi al
				const dataQuery = `SELECT * FROM orders ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`;
				const dataParams = [...params, limit, offset];

				this.db.all(dataQuery, dataParams, (err, rows) => {
					if (err) {
						console.error("Error searching orders:", err);
						reject(err);
					} else {
						resolve({
							orders: rows,
							total: total,
							currentPage: page,
							totalPages: totalPages,
							limit: limit,
						});
					}
				});
			});
		});
	}

	// Sayfalanmış siparişleri getir
	async getPaginatedOrders(page = 1, limit = 10) {
		return this.searchOrders("", page, limit);
	}

	// Admin kullanıcı doğrulama
	async validateAdminUser(username, password) {
		return new Promise((resolve, reject) => {
			const query = `SELECT * FROM admin_users WHERE username = ?`;

			this.db.get(query, [username], async (err, row) => {
				if (err) {
					console.error("Error validating admin user:", err);
					resolve(false); // Hata durumunda false döndür
					return;
				}

				if (!row) {
					resolve(false);
					return;
				}

				try {
					const isValid = await bcrypt.compare(
						password,
						row.password
					);
					resolve(isValid);
				} catch (bcryptErr) {
					console.error("Error comparing passwords:", bcryptErr);
					resolve(false);
				}
			});
		});
	}

	// Admin kullanıcıyı username ile getir
	async getAdminByUsername(username) {
		return new Promise((resolve, reject) => {
			const query = `SELECT * FROM admin_users WHERE username = ?`;

			this.db.get(query, [username], (err, row) => {
				if (err) {
					console.error("Error getting admin user:", err);
					reject(err);
				} else {
					resolve(row);
				}
			});
		});
	}

	// Admin kullanıcıyı ID ile getir
	async getAdminById(id) {
		return new Promise((resolve, reject) => {
			const query = `SELECT * FROM admin_users WHERE id = ?`;
			this.db.get(query, [id], (err, row) => {
				if (err) {
					reject(err);
				} else {
					resolve(row);
				}
			});
		});
	}

	// Admin ayarlarını getir
	async getAdminSettings(username) {
		return new Promise((resolve, reject) => {
			const query = `SELECT id, username, email FROM admin_users WHERE username = ?`;
			this.db.get(query, [username], (err, row) => {
				if (err) {
					reject(err);
				} else {
					resolve(
						row || {
							id: null,
							username: username,
							email: "",
							notification_email: "",
						}
					);
				}
			});
		});
	}

	// Admin kullanıcıyı güncelle
	async updateAdminUser(adminId, updates) {
		return new Promise(async (resolve, reject) => {
			try {
				const fields = [];
				const values = [];

				if (updates.username) {
					fields.push("username = ?");
					values.push(updates.username);
				}
				if (updates.email) {
					fields.push("email = ?");
					values.push(updates.email);
				}
				if (updates.password) {
					const hashedPassword = await bcrypt.hash(
						updates.password,
						10
					);
					fields.push("password = ?");
					values.push(hashedPassword);
				}

				fields.push("updated_at = CURRENT_TIMESTAMP");
				values.push(adminId);

				const query = `UPDATE admin_users SET ${fields.join(
					", "
				)} WHERE id = ?`;

				this.db.run(query, values, function (err) {
					if (err) {
						console.error("Error updating admin user:", err);
						reject(err);
					} else {
						resolve({ changes: this.changes });
					}
				});
			} catch (error) {
				reject(error);
			}
		});
	}

	// Admin şifresini güncelle
	async updateAdminPassword(username, newPassword) {
		return new Promise(async (resolve, reject) => {
			try {
				const hashedPassword = await bcrypt.hash(newPassword, 12);
				const query = `UPDATE admin_users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE username = ?`;

				this.db.run(query, [hashedPassword, username], function (err) {
					if (err) {
						console.error("Error updating admin password:", err);
						reject(err);
					} else {
						console.log("Admin password updated for:", username);
						resolve({ changes: this.changes });
					}
				});
			} catch (error) {
				reject(error);
			}
		});
	}

	// Geriye dönük uyumluluk için alias fonksiyonlar
	async validateAdminCredentials(username, password) {
		return await this.validateAdminUser(username, password);
	}

	async getAdminUser(username) {
		return await this.getAdminByUsername(username);
	}

	// Admin kullanıcıları temizle (test için)
	async clearAdminUsers() {
		return new Promise((resolve, reject) => {
			this.db.run("DELETE FROM admin_users", (err) => {
				if (err) {
					console.error("Error clearing admin users:", err);
					reject(err);
				} else {
					resolve();
				}
			});
		});
	}

	// Sipariş sil
	async deleteOrder(orderId) {
		return new Promise((resolve, reject) => {
			const query = `DELETE FROM orders WHERE id = ?`;

			this.db.run(query, [orderId], function (err) {
				if (err) {
					console.error("Error deleting order:", err);
					reject(err);
				} else {
					console.log(
						"Order deleted:",
						orderId,
						"- Changes:",
						this.changes
					);
					resolve({ changes: this.changes, deletedId: orderId });
				}
			});
		});
	}

	// Veritabanını kapat
	close() {
		this.db.close((err) => {
			if (err) {
				console.error("Error closing database:", err);
			} else {
				console.log("Database connection closed.");
			}
		});
	}
}

module.exports = new Database();
