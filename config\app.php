<?php

declare(strict_types=1);

/**
 * Application Configuration
 * Laravel-style app config
 */

return [
    'name' => $_ENV['APP_NAME'] ?? 'DrxDion',
    'env' => $_ENV['APP_ENV'] ?? 'production',
    'debug' => $_ENV['DEBUG'] ?? false,
    'url' => $_ENV['BASE_URL'] ?? 'https://drxdion.test',
    'timezone' => 'Europe/Istanbul',

    'admin' => [
        'default_username' => 'admin',
        'default_password' => 'admin123',
        'session_timeout' => 3600, // 1 hour
    ],

    'payment' => [
        'iyzico' => [
            'api_key' => $_ENV['IYZIPAY_API_KEY'] ?? '',
            'secret_key' => $_ENV['IYZIPAY_SECRET_KEY'] ?? '',
            'base_url' => $_ENV['IYZIPAY_URI'] ?? 'https://sandbox-api.iyzipay.com',
        ],
    ],

    'mail' => [
        'from' => [
            'address' => $_ENV['EMAIL_USER'] ?? '<EMAIL>',
            'name' => $_ENV['APP_NAME'] ?? 'DrxDion',
        ],
        'notification_to' => $_ENV['NOTIFICATION_EMAIL'] ?? $_ENV['ORDER_NOTIFICATION_TO'] ?? '',
    ],
];
