<?php

declare(strict_types=1);

namespace DrxDion\Interfaces;

/**
 * Payment Gateway Interface
 * Contract for payment processing services
 */
interface PaymentGatewayInterface
{
    /**
     * Initialize payment
     */
    public function initializePayment(array $orderData): array;

    /**
     * Verify payment callback
     */
    public function verifyCallback(array $callbackData): array;

    /**
     * Process webhook
     */
    public function processWebhook(string $rawData): array;

    /**
     * Get payment status
     */
    public function getPaymentStatus(string $paymentId): string;
}
