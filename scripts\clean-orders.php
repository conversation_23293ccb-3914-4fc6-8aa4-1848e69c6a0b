<?php

declare(strict_types=1);

/**
 * Clean Orders Script
 * PHP equivalent of npm run clean-orders
 */

require_once __DIR__ . '/../config.php';

try {
    echo "🧹 Cleaning all orders from database...\n";

    // Get database connection
    $container = new \DrxDion\Infrastructure\DependencyInjection\Container();
    require_once __DIR__ . '/../src/Infrastructure/DependencyInjection/services.php';
    registerServices($container);

    $database = $container->get(\DrxDion\Infrastructure\Persistence\SQLite\DatabaseConnection::class);
    $connection = $database->getConnection();

    // Start transaction
    $connection->beginTransaction();

    // Delete all orders
    $stmt = $connection->prepare("DELETE FROM orders");
    $stmt->execute();
    $deletedCount = $stmt->rowCount();

    // Reset auto-increment (SQLite uses sqlite_sequence)
    $stmt = $connection->prepare("DELETE FROM sqlite_sequence WHERE name = 'orders'");
    $stmt->execute();

    $connection->commit();

    echo "✅ Deleted $deletedCount orders\n";
    echo "🔄 Reset order ID counter\n";
    echo "✅ Orders cleaned successfully!\n";
} catch (\Exception $e) {
    if (isset($connection) && $connection->inTransaction()) {
        $connection->rollBack();
    }

    echo "❌ Error cleaning orders: " . $e->getMessage() . "\n";
    exit(1);
}
