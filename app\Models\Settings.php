<?php

declare(strict_types=1);

namespace App\Models;

/**
 * Settings Model
 * Represents application settings
 */
final class Settings
{
    private string $key;
    private string $value;
    private string $description;
    private string $type;
    private ?string $category;
    private ?int $id;
    private ?\DateTimeImmutable $updatedAt;
    private ?\DateTimeImmutable $createdAt;

    public function __construct(
        string $key,
        string $value,
        string $description = '',
        string $type = 'string',
        ?string $category = null,
        ?int $id = null,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $createdAt = null
    ) {
        $this->key = $key;
        $this->value = $value;
        $this->description = $description;
        $this->type = $type;
        $this->category = $category;
        $this->id = $id;
        $this->updatedAt = $updatedAt;
        $this->createdAt = $createdAt;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getKey(): string
    {
        return $this->key;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'key' => $this->key,
            'value' => $this->value,
            'description' => $this->description,
            'type' => $this->type,
            'category' => $this->category,
            'created_at' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt?->format('Y-m-d H:i:s')
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            key: $data['key'] ?? '',
            value: $data['value'] ?? '',
            description: $data['description'] ?? '',
            type: $data['type'] ?? 'string',
            category: $data['category'] ?? null,
            id: isset($data['id']) ? (int) $data['id'] : null,
            createdAt: isset($data['created_at']) ?
                new \DateTimeImmutable($data['created_at']) : null,
            updatedAt: isset($data['updated_at']) ?
                new \DateTimeImmutable($data['updated_at']) : null
        );
    }

    public static function getAll(): array
    {
        try {
            $config = require dirname(__DIR__, 2) . '/config/database.php';
            $mysql = $config['connections']['mysql'];

            $dsn = "mysql:host={$mysql['host']};port={$mysql['port']};dbname={$mysql['database']};charset=utf8mb4";
            $pdo = new \PDO($dsn, $mysql['username'], $mysql['password'], [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false
            ]);

            // Check if settings table exists
            $tableCheck = $pdo->query("SHOW TABLES LIKE 'settings'");
            if ($tableCheck->rowCount() === 0) {
                return self::getFromJSON();
            }

            $stmt = $pdo->query('SELECT * FROM settings');
            $settings = [];

            while ($row = $stmt->fetch()) {
                $settings[$row['key']] = $row['value'];
            }

            return $settings;
        } catch (\Exception $e) {
            return self::getFromJSON();
        }
    }

    public static function updateMultiple(array $settings): bool
    {
        try {
            $config = require dirname(__DIR__, 2) . '/config/database.php';
            $mysql = $config['connections']['mysql'];

            $dsn = "mysql:host={$mysql['host']};port={$mysql['port']};dbname={$mysql['database']};charset=utf8mb4";
            $pdo = new \PDO($dsn, $mysql['username'], $mysql['password'], [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false
            ]);

            $pdo->beginTransaction();

            foreach ($settings as $key => $value) {
                $stmt = $pdo->prepare('
                    INSERT INTO settings (key, value, updated_at) 
                    VALUES (?, ?, NOW()) 
                    ON DUPLICATE KEY UPDATE value = VALUES(value), updated_at = NOW()
                ');
                $stmt->execute([$key, $value]);
            }

            $pdo->commit();

            // Also update JSON backup
            self::updateJSON($settings);

            return true;
        } catch (\Exception $e) {
            if (isset($pdo)) {
                $pdo->rollBack();
            }
            // Fallback to JSON
            return self::updateJSON($settings);
        }
    }

    private static function getFromJSON(): array
    {
        $jsonFile = dirname(__DIR__, 2) . '/data/settings.json';

        if (!file_exists($jsonFile)) {
            // Create default settings file
            $defaultSettings = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'notification_email' => '<EMAIL>',
                'site_name' => 'DrxDion',
                'timezone' => 'Europe/Istanbul',
                'currency' => 'USD',
                'language' => 'en'
            ];

            // Ensure data directory exists
            $dataDir = dirname($jsonFile);
            if (!is_dir($dataDir)) {
                mkdir($dataDir, 0755, true);
            }

            file_put_contents($jsonFile, json_encode($defaultSettings, JSON_PRETTY_PRINT));
            return $defaultSettings;
        }

        $content = file_get_contents($jsonFile);
        if (empty($content)) {
            return [];
        }

        $data = json_decode($content, true);
        return $data ?? [];
    }

    private static function updateJSON(array $settings): bool
    {
        $dataDir = dirname(__DIR__, 2) . '/data';
        if (!is_dir($dataDir)) {
            mkdir($dataDir, 0755, true);
        }

        $jsonFile = $dataDir . '/settings.json';

        // Get existing settings
        $existingSettings = self::getFromJSON();

        // Merge with new settings
        $allSettings = array_merge($existingSettings, $settings);

        return file_put_contents($jsonFile, json_encode($allSettings, JSON_PRETTY_PRINT)) !== false;
    }
}
