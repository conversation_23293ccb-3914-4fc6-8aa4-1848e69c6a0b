<?php

declare(strict_types=1);

/**
 * Generate Secrets Script
 * PHP equivalent of npm run generate-secrets
 */

echo "🔐 Generating secure secrets for DrxDion...\n\n";

// Generate session secret
$sessionSecret = bin2hex(random_bytes(32));
echo "SESSION_SECRET=" . $sessionSecret . "\n";

// Generate webhook secret
$webhookSecret = bin2hex(random_bytes(32));
echo "IYZICO_WEBHOOK_SECRET=" . $webhookSecret . "\n";

echo "\n✅ Secrets generated successfully!\n";
echo "📝 Copy these values to your .env file.\n";
echo "⚠️  Keep these secrets secure and never commit them to version control.\n";
