<?php

/**
 * Performance Optimization Script
 * Production ortamı için performans optimizasyonları
 */

declare(strict_types=1);

echo "=== DRXDION PERFORMANCE OPTIMIZATION ===\n\n";

// 1. CSS/JS dosyalarını minify et
echo "1. Asset Optimization:\n";

// CSS minification function
function minifyCSS($css)
{
    // Remove comments
    $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
    // Remove tabs, spaces, newlines, etc.
    $css = str_replace(["\r\n", "\r", "\n", "\t", '  ', '    ', '    '], '', $css);
    // Remove unnecessary spaces
    $css = str_replace(['; ', ' {', '{ ', ' }', '} ', ': ', ' ,', ', '], [';', '{', '{', '}', '}', ':', ',', ','], $css);
    return $css;
}

// JS minification function (basic)
function minifyJS($js)
{
    // Remove comments
    $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
    $js = preg_replace('/\/\/.*/', '', $js);
    // Remove unnecessary whitespace
    $js = preg_replace('/\s+/', ' ', $js);
    return trim($js);
}

// Minify admin panel CSS files
$adminPages = ['orders', 'settings', 'storage'];
foreach ($adminPages as $page) {
    $file = "public/admin/{$page}.html";
    if (file_exists($file)) {
        $content = file_get_contents($file);

        // Extract and minify CSS
        if (preg_match('/<style>(.*?)<\/style>/s', $content, $matches)) {
            $minifiedCSS = minifyCSS($matches[1]);
            $content = str_replace($matches[1], $minifiedCSS, $content);
            file_put_contents($file, $content);
            echo "   ✅ Minified CSS in $file\n";
        }
    }
}

// 2. Image optimization kontrolü
echo "\n2. Image Optimization:\n";
$imageDir = 'public/uploads';
if (is_dir($imageDir)) {
    $images = glob($imageDir . '/*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);
    $totalSize = 0;
    $count = 0;

    foreach ($images as $image) {
        $size = filesize($image);
        $totalSize += $size;
        $count++;

        // Büyük dosyaları tespit et
        if ($size > 500000) { // 500KB'dan büyük
            echo "   ⚠️  Large image: " . basename($image) . " (" . round($size / 1024) . "KB)\n";
        }
    }

    if ($count > 0) {
        echo "   📊 Total images: $count (Total size: " . round($totalSize / 1024 / 1024, 2) . "MB)\n";
        if ($totalSize > 10485760) { // 10MB'dan büyük
            echo "   ⚠️  Consider optimizing images to reduce total size\n";
        }
    } else {
        echo "   ✅ No images found\n";
    }
} else {
    echo "   ⚠️  Uploads directory not found\n";
}

// 3. Database optimization
echo "\n3. Database Optimization:\n";
try {
    require_once 'config.php';

    // Check if we can connect (will fail but that's ok for now)
    echo "   ℹ️  Database optimization requires manual configuration\n";
    echo "   📋 Recommended MySQL optimizations:\n";
    echo "      - Add indexes on frequently queried columns\n";
    echo "      - Enable query cache\n";
    echo "      - Optimize table structures\n";
    echo "      - Regular ANALYZE TABLE commands\n";
} catch (Exception $e) {
    echo "   ℹ️  Database connection needed for optimization\n";
}

// 4. Caching recommendations
echo "\n4. Caching Setup:\n";
$htaccessContent = file_exists('.htaccess') ? file_get_contents('.htaccess') : '';

// Add browser caching rules
$cachingRules = '
# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# Cache-Control Headers
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|woff|woff2)$">
        Header set Cache-Control "max-age=31536000, public"
    </FilesMatch>
</IfModule>';

if (strpos($htaccessContent, 'mod_expires.c') === false) {
    file_put_contents('.htaccess', $htaccessContent . $cachingRules);
    echo "   ✅ Browser caching rules added to .htaccess\n";
} else {
    echo "   ✅ Browser caching already configured\n";
}

// 5. JSON file optimization
echo "\n5. Data File Optimization:\n";
$dataFile = 'data/orders.json';
if (file_exists($dataFile)) {
    $content = file_get_contents($dataFile);
    $data = json_decode($content, true);

    if ($data) {
        $count = count($data);
        $size = filesize($dataFile);
        echo "   📊 Orders in JSON: $count (File size: " . round($size / 1024) . "KB)\n";

        // Compact JSON (remove unnecessary spaces)
        $compactJson = json_encode($data, JSON_UNESCAPED_UNICODE);
        if (strlen($compactJson) < strlen($content)) {
            file_put_contents($dataFile, $compactJson);
            $saved = strlen($content) - strlen($compactJson);
            echo "   ✅ JSON file compacted (Saved: " . round($saved / 1024) . "KB)\n";
        } else {
            echo "   ✅ JSON file already optimized\n";
        }
    }
} else {
    echo "   ℹ️  Orders JSON file not found\n";
}

echo "\n=== PERFORMANCE OPTIMIZATION COMPLETE ===\n";
echo "\nADDITIONAL RECOMMENDATIONS:\n";
echo "1. Enable PHP OPcache on server\n";
echo "2. Use CDN for static assets\n";
echo "3. Enable Gzip compression (already configured)\n";
echo "4. Monitor server resource usage\n";
echo "5. Implement database connection pooling\n";
echo "6. Consider Redis for session storage\n";
echo "7. Optimize server-side caching\n";
echo "8. Remove this optimization script after use\n";
