<?php

declare(strict_types=1);

namespace DrxDion\Controllers;

/**
 * Page Controller
 * Handles public page requests
 */
final class PageController
{
    public function index(array $request): void
    {
        // Serve the main index.html file
        $indexPath = dirname(__DIR__, 3) . '/public/index.html';

        if (file_exists($indexPath)) {
            header('Content-Type: text/html; charset=utf-8');
            readfile($indexPath);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Page not found']);
        }
    }
    public function contact(array $request): void
    {
        header('Content-Type: application/json');

        try {
            // Basic contact form handling
            $data = $request['body'];

            if (empty($data['name']) || empty($data['email']) || empty($data['message'])) {
                http_response_code(400);
                echo json_encode(['error' => 'All fields are required']);
                return;
            }

            // Here you would normally send an email
            // For now, just return success
            echo json_encode(['success' => true, 'message' => 'Message sent successfully']);
        } catch (\Throwable $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to send message']);
        }
    }

    public function csrfToken(array $request): void
    {
        header('Content-Type: application/json');

        // Get or generate CSRF token
        if (empty($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }

        $token = $_SESSION['csrf_token'];

        // Also set as cookie for JavaScript access
        setcookie('csrf-token', $token, [
            'path' => '/',
            'httponly' => false,
            'secure' => isset($_SERVER['HTTPS']),
            'samesite' => 'Strict'
        ]);

        echo json_encode(['token' => $token]);
    }
}
