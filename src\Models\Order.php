<?php

declare(strict_types=1);

namespace DrxDion\Models;

use DateTimeImmutable;

/**
 * Order Entity
 * Core business entity representing an order
 */
final class Order
{
    public function __construct(
        private string $id,
        private string $productName,
        private float $price,
        private float $totalPrice,
        private float $shippingCost,
        private string $paymentMethod,
        private string $paymentStatus,
        private ?string $paymentId,
        private string $customerName,
        private string $customerEmail,
        private string $shippingAddress,
        private DateTimeImmutable $createdAt,
        private DateTimeImmutable $updatedAt
    ) {}

    public static function create(
        string $id,
        string $productName,
        float $price,
        float $totalPrice,
        float $shippingCost,
        string $paymentMethod,
        string $customerName,
        string $customerEmail,
        string $shippingAddress
    ): self {
        $now = new DateTimeImmutable();

        return new self(
            $id,
            $productName,
            $price,
            $totalPrice,
            $shippingCost,
            $paymentMethod,
            'pending',
            null,
            $customerName,
            $customerEmail,
            $shippingAddress,
            $now,
            $now
        );
    }

    public function updatePaymentStatus(string $status, ?string $paymentId = null): self
    {
        return new self(
            $this->id,
            $this->productName,
            $this->price,
            $this->totalPrice,
            $this->shippingCost,
            $this->paymentMethod,
            $status,
            $paymentId ?? $this->paymentId,
            $this->customerName,
            $this->customerEmail,
            $this->shippingAddress,
            $this->createdAt,
            new DateTimeImmutable()
        );
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getProductName(): string
    {
        return $this->productName;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function getTotalPrice(): float
    {
        return $this->totalPrice;
    }

    public function getShippingCost(): float
    {
        return $this->shippingCost;
    }

    public function getPaymentMethod(): string
    {
        return $this->paymentMethod;
    }

    public function getPaymentStatus(): string
    {
        return $this->paymentStatus;
    }

    public function getPaymentId(): ?string
    {
        return $this->paymentId;
    }

    public function getCustomerName(): string
    {
        return $this->customerName;
    }

    public function getCustomerEmail(): string
    {
        return $this->customerEmail;
    }

    public function getShippingAddress(): string
    {
        return $this->shippingAddress;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'product_name' => $this->productName,
            'price' => $this->price,
            'total_price' => $this->totalPrice,
            'shipping_cost' => $this->shippingCost,
            'payment_method' => $this->paymentMethod,
            'payment_status' => $this->paymentStatus,
            'payment_id' => $this->paymentId,
            'customer_name' => $this->customerName,
            'customer_email' => $this->customerEmail,
            'shipping_address' => $this->shippingAddress,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s'),
        ];
    }
}
