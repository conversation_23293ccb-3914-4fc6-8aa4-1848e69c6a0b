<?php

declare(strict_types=1);

echo "=== SIMPLE ROUTER TEST ===\n";

// Test basic routing
$uri = $_SERVER['REQUEST_URI'] ?? '/';
$method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

echo "URI: $uri\n";
echo "Method: $method\n";

// Clean up URI
if (str_starts_with($uri, '/drxdion/')) {
    $uri = substr($uri, 8);
}
if (empty($uri)) {
    $uri = '/';
}

echo "Cleaned URI: $uri\n";

// Load routes
$webRoutes = require __DIR__ . '/../routes/web.php';

echo "Available routes:\n";
foreach ($webRoutes as $route => $config) {
    echo "  $route => {$config[0]} {$config[1]}\n";
}

// Test route matching
if (isset($webRoutes[$uri])) {
    echo "MATCH FOUND: $uri\n";
    $route = $webRoutes[$uri];
    echo "Controller: {$route[1]}\n";
} else {
    echo "NO MATCH for: $uri\n";
}

?>
