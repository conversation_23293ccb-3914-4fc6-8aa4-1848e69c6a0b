<?php

declare(strict_types=1);

namespace DrxDion\Interfaces;

/**
 * Email Service Interface
 * Contract for email services
 */
interface EmailServiceInterface
{
    /**
     * Send order notification email
     */
    public function sendOrderNotification(array $orderData): bool;

    /**
     * Send contact form email
     */
    public function sendContactForm(array $contactData): bool;

    /**
     * Test email configuration
     */
    public function testConfiguration(): bool;
}
