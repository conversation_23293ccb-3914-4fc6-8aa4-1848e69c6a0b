# DrxDion Production Environment Configuration Guide

## 🔒 SECURITY WARNING
Never commit your .env file to version control!
Keep your secrets secure and change default values.

## 📝 Production Setup Checklist

### 1. Email Configuration
- Use a dedicated business email account
- Enable 2FA and create app-specific password for Gmail
- Set proper FROM addresses for professional appearance

### 2. Session Security
- Generate a strong SESSION_SECRET (minimum 32 characters)
- Use a cryptographically secure random string
- Command to generate: `node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"`

### 3. Iyzico Payment Gateway
- Switch from sandbox to production credentials
- Update IYZIPAY_URI to production endpoint
- Configure webhook URL in Iyzico dashboard
- Set strong webhook secret

### 4. reCAPTCHA
- Create production keys for your domain
- Update both site key and secret key
- Configure allowed domains in Google console

### 5. Domain and URLs
- Update BASE_URL to your production domain
- Ensure SSL certificate is properly configured
- Update webhook URLs to production endpoints

## 🛡️ Security Best Practices

1. **Strong Secrets**: All secret keys should be cryptographically secure
2. **Environment Separation**: Never use development keys in production
3. **Regular Rotation**: Rotate secrets periodically
4. **Monitoring**: Set up alerts for failed authentications
5. **Backup**: Secure backup of environment configuration

## 🔄 Quick Setup Commands

```bash
# Generate secure session secret
node -e "console.log('SESSION_SECRET=' + require('crypto').randomBytes(32).toString('hex'))"

# Generate webhook secret
node -e "console.log('IYZICO_WEBHOOK_SECRET=' + require('crypto').randomBytes(32).toString('hex'))"

# Test email configuration
npm run test-email

# Verify all environment variables
npm run check-env
```

## 📧 Email Templates

The system uses these email templates:
- Order confirmation to customer
- Order notification to admin
- Payment success confirmation
- Order status updates

Make sure your email configuration supports HTML emails and has proper SPF/DKIM setup.
