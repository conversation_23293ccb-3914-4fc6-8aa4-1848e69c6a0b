<?php

/**
 * Database Setup
 * Kullanım: php create-db.php
 * Not: <PERSON><PERSON><PERSON> sonrası dosyay<PERSON> silin (rm create-db.php)
 */

require_once __DIR__ . '/config.php';

try {
    echo "Creating new database...\n";

    // Direct database connection
    $dbPath = __DIR__ . '/data/orders.db';
    echo "Database path: $dbPath\n";

    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create orders table with all required columns
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS orders (
            id TEXT PRIMARY KEY,
            customer_name TEXT NOT NULL,
            customer_email TEXT NOT NULL,
            customer_phone TEXT,
            product_name TEXT NOT NULL,
            price REAL NOT NULL,
            total_amount REAL NOT NULL,
            shipping_cost REAL DEFAULT 0,
            payment_method TEXT NOT NULL,
            payment_status TEXT DEFAULT 'pending',
            payment_id TEXT,
            shipping_address TEXT NOT NULL,
            items TEXT,
            status TEXT DEFAULT 'pending',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");

    // Create admin_users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            email TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");

    echo "Database created successfully!\n";

    // Verify orders table structure
    $result = $pdo->query("PRAGMA table_info(orders)");
    echo "\nOrders table columns:\n";
    foreach ($result as $column) {
        echo "- " . $column['name'] . " (" . $column['type'] . ")\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
