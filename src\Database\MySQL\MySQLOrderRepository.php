<?php

declare(strict_types=1);

namespace DrxDion\Database\MySQL;

use DrxDion\Interfaces\OrderRepositoryInterface;
use DrxDion\Models\Order;
use DateTimeImmutable;
use PDOException;
use RuntimeException;

/**
 * MySQL Order Repository Implementation
 */
final class MySQLOrderRepository implements OrderRepositoryInterface
{
    public function __construct(
        private MySQLConnection $database
    ) {}

    public function save(Order $order): void
    {
        try {
            $sql = "
                INSERT INTO orders (
                    id, customer_name, customer_email, customer_phone,
                    shipping_address, total_amount, shipping_cost, payment_method, items,
                    status, payment_status, payment_id,
                    created_at, updated_at
                ) VALUES (
                    :id, :customer_name, :customer_email, :customer_phone,
                    :shipping_address, :total_amount, :shipping_cost, :payment_method, :items,
                    :status, :payment_status, :payment_id,
                    :created_at, :updated_at
                )
                ON DUPLICATE KEY UPDATE
                    customer_name = VALUES(customer_name),
                    customer_email = VALUES(customer_email),
                    customer_phone = VALUES(customer_phone),
                    shipping_address = VALUES(shipping_address),
                    total_amount = VALUES(total_amount),
                    shipping_cost = VALUES(shipping_cost),
                    payment_method = VALUES(payment_method),
                    items = VALUES(items),
                    status = VALUES(status),
                    payment_status = VALUES(payment_status),
                    payment_id = VALUES(payment_id),
                    updated_at = VALUES(updated_at)
            ";

            // Create items JSON from order data
            $items = [
                [
                    'name' => $order->getProductName(),
                    'price' => $order->getPrice(),
                    'quantity' => 1
                ]
            ];

            $stmt = $this->database->getConnection()->prepare($sql);

            $stmt->execute([
                ':id' => $order->getId(),
                ':customer_name' => $order->getCustomerName(),
                ':customer_email' => $order->getCustomerEmail(),
                ':customer_phone' => '555-0000', // Default phone
                ':shipping_address' => $order->getShippingAddress(),
                ':total_amount' => $order->getTotalPrice(),
                ':shipping_cost' => $order->getShippingCost(),
                ':payment_method' => $order->getPaymentMethod(),
                ':items' => json_encode($items),
                ':status' => 'pending',
                ':payment_status' => $order->getPaymentStatus(),
                ':payment_id' => $order->getPaymentId(),
                ':created_at' => $order->getCreatedAt()->format('Y-m-d H:i:s'),
                ':updated_at' => $order->getUpdatedAt()->format('Y-m-d H:i:s')
            ]);
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to save order: " . $e->getMessage(), 0, $e);
        }
    }

    public function findById(string $id): ?Order
    {
        try {
            $sql = "SELECT * FROM orders WHERE id = :id LIMIT 1";
            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute([':id' => $id]);

            $row = $stmt->fetch();

            return $row ? $this->rowToOrder($row) : null;
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to find order: " . $e->getMessage(), 0, $e);
        }
    }

    public function findAll(int $limit = 100, int $offset = 0): array
    {
        try {
            $sql = "
                SELECT * FROM orders 
                ORDER BY created_at DESC 
                LIMIT :limit OFFSET :offset
            ";

            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->bindValue(':limit', $limit, \PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, \PDO::PARAM_INT);
            $stmt->execute();

            $orders = [];
            while ($row = $stmt->fetch()) {
                $orders[] = $this->rowToOrder($row);
            }

            return $orders;
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to retrieve orders: " . $e->getMessage(), 0, $e);
        }
    }

    public function updatePayment(string $id, ?string $paymentId, string $status): bool
    {
        try {
            $sql = "
                UPDATE orders 
                SET payment_id = :payment_id, payment_status = :status, updated_at = NOW()
                WHERE id = :id
            ";

            $stmt = $this->database->getConnection()->prepare($sql);

            return $stmt->execute([
                ':id' => $id,
                ':payment_id' => $paymentId,
                ':status' => $status
            ]);
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to update payment: " . $e->getMessage(), 0, $e);
        }
    }

    public function updateStatus(string $id, string $status): bool
    {
        try {
            $sql = "
                UPDATE orders 
                SET payment_status = :status, updated_at = NOW()
                WHERE id = :id
            ";

            $stmt = $this->database->getConnection()->prepare($sql);

            return $stmt->execute([
                ':id' => $id,
                ':status' => $status
            ]);
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to update status: " . $e->getMessage(), 0, $e);
        }
    }

    public function findByStatus(string $status, int $limit = 100): array
    {
        try {
            $sql = "
                SELECT * FROM orders 
                WHERE payment_status = :status 
                ORDER BY created_at DESC 
                LIMIT :limit
            ";

            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->bindValue(':status', $status, \PDO::PARAM_STR);
            $stmt->bindValue(':limit', $limit, \PDO::PARAM_INT);
            $stmt->execute();

            $orders = [];
            while ($row = $stmt->fetch()) {
                $orders[] = $this->rowToOrder($row);
            }

            return $orders;
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to find orders by status: " . $e->getMessage(), 0, $e);
        }
    }

    public function countAll(): int
    {
        return $this->count();
    }

    public function countByStatus(string $status): int
    {
        try {
            $sql = "SELECT COUNT(*) FROM orders WHERE payment_status = :status";
            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute([':status' => $status]);

            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to count orders by status: " . $e->getMessage(), 0, $e);
        }
    }

    public function delete(string $id): bool
    {
        try {
            $sql = "DELETE FROM orders WHERE id = :id";
            $stmt = $this->database->getConnection()->prepare($sql);

            return $stmt->execute([':id' => $id]);
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to delete order: " . $e->getMessage(), 0, $e);
        }
    }

    public function count(): int
    {
        try {
            $sql = "SELECT COUNT(*) FROM orders";
            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute();

            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to count orders: " . $e->getMessage(), 0, $e);
        }
    }

    public function search(array $criteria, int $limit = 20, int $offset = 0): array
    {
        try {
            $conditions = [];
            $params = [];

            // Build WHERE conditions based on criteria
            if (!empty($criteria['customer_name'])) {
                // Search in customer name, email, and order ID
                $conditions[] = "(customer_name LIKE ? OR customer_email LIKE ? OR id LIKE ?)";
                $searchTerm = '%' . $criteria['customer_name'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            if (!empty($criteria['customer_email'])) {
                $conditions[] = "customer_email LIKE ?";
                $params[] = '%' . $criteria['customer_email'] . '%';
            }

            if (!empty($criteria['payment_status'])) {
                $conditions[] = "payment_status = ?";
                $params[] = $criteria['payment_status'];
            }

            if (!empty($criteria['status'])) {
                $conditions[] = "payment_status = ?";
                $params[] = $criteria['status'];
            }

            if (!empty($criteria['payment_method'])) {
                $conditions[] = "payment_method = ?";
                $params[] = $criteria['payment_method'];
            }

            if (!empty($criteria['date_from'])) {
                try {
                    $dateFrom = new \DateTime($criteria['date_from']);
                    $conditions[] = "created_at >= ?";
                    $params[] = $dateFrom->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    // Tarih formatı geçersizse bu filtreyi atla
                }
            }

            if (!empty($criteria['date_to'])) {
                try {
                    $dateTo = new \DateTime($criteria['date_to']);
                    $conditions[] = "created_at <= ?";
                    $params[] = $dateTo->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    // Tarih formatı geçersizse bu filtreyi atla
                }
            }

            $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);

            $sql = "
                SELECT * FROM orders 
                {$whereClause}
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ";

            // Add limit and offset to params
            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute($params);

            $orders = [];
            while ($row = $stmt->fetch()) {
                $orders[] = $this->rowToOrder($row);
            }

            return $orders;
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to search orders: " . $e->getMessage(), 0, $e);
        }
    }



    public function countWithCriteria(array $criteria): int
    {
        try {
            $conditions = [];
            $params = [];

            // Build WHERE conditions (same as search method)
            if (!empty($criteria['customer_name'])) {
                $conditions[] = "(customer_name LIKE ? OR customer_email LIKE ? OR id LIKE ?)";
                $searchTerm = '%' . $criteria['customer_name'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            if (!empty($criteria['customer_email'])) {
                $conditions[] = "customer_email LIKE ?";
                $params[] = '%' . $criteria['customer_email'] . '%';
            }

            if (!empty($criteria['payment_status'])) {
                $conditions[] = "payment_status = ?";
                $params[] = $criteria['payment_status'];
            }

            if (!empty($criteria['status'])) {
                $conditions[] = "payment_status = ?";
                $params[] = $criteria['status'];
            }

            if (!empty($criteria['payment_method'])) {
                $conditions[] = "payment_method = ?";
                $params[] = $criteria['payment_method'];
            }

            if (!empty($criteria['date_from'])) {
                try {
                    $dateFrom = new \DateTime($criteria['date_from']);
                    $conditions[] = "created_at >= ?";
                    $params[] = $dateFrom->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    // Tarih formatı geçersizse bu filtreyi atla
                }
            }

            if (!empty($criteria['date_to'])) {
                try {
                    $dateTo = new \DateTime($criteria['date_to']);
                    $conditions[] = "created_at <= ?";
                    $params[] = $dateTo->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    // Tarih formatı geçersizse bu filtreyi atla
                }
            }

            $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);

            $sql = "SELECT COUNT(*) as count FROM orders {$whereClause}";

            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);

            return (int) ($result['count'] ?? 0);
        } catch (PDOException $e) {
            throw new RuntimeException('Failed to count orders with criteria: ' . $e->getMessage(), 0, $e);
        }
    }

    private function rowToOrder(array $row): Order
    {
        // Parse items JSON to extract product info
        $items = json_decode($row['items'] ?? '[]', true);

        // Extract product info from items
        $productName = '';
        $price = 0.0;
        if (!empty($items) && is_array($items)) {
            $firstItem = $items[0];
            $productName = $firstItem['name'] ?? 'Unknown Product';
            $price = (float) ($firstItem['price'] ?? 0);
        }

        // Calculate total price from total_amount
        $totalPrice = (float) $row['total_amount'];

        // Get payment method from database
        $paymentMethod = $row['payment_method'] ?? 'cod';

        return new Order(
            $row['id'],
            $productName,
            $price,
            $totalPrice,
            (float) $row['shipping_cost'],
            $paymentMethod,
            $row['payment_status'],
            $row['payment_id'],
            $row['customer_name'],
            $row['customer_email'],
            $row['shipping_address'],
            new DateTimeImmutable($row['created_at']),
            new DateTimeImmutable($row['updated_at'])
        );
    }

    private function mapRowToOrder(array $row): Order
    {
        // Parse items JSON to extract product info
        $items = json_decode($row['items'] ?? '[]', true);

        // Extract product info from items
        $productName = '';
        $price = 0.0;
        if (!empty($items) && is_array($items)) {
            $firstItem = $items[0];
            $productName = $firstItem['name'] ?? 'Unknown Product';
            $price = (float) ($firstItem['price'] ?? 0);
        }

        // Calculate total price from total_amount
        $totalPrice = (float) $row['total_amount'];

        // Default payment method
        $paymentMethod = 'credit_card';

        return new Order(
            $row['id'],
            $productName,
            $price,
            $totalPrice,
            (float) $row['shipping_cost'],
            $paymentMethod,
            $row['payment_status'],
            $row['payment_id'],
            $row['customer_name'],
            $row['customer_email'],
            $row['shipping_address'],
            new DateTimeImmutable($row['created_at']),
            new DateTimeImmutable($row['updated_at'])
        );
    }
}
