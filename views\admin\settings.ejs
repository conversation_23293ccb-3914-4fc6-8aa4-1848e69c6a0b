<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Admin Settings - DrxDion</title>
		<link href="/assets/css/bootstrap.min.css" rel="stylesheet" />
		<link rel="stylesheet" href="/assets/css/bootstrap-icons.css" />
		<style>
			body {
				background-color: #f8f9fa;
				padding: 2rem;
			}
			.admin-header {
				background: #f8f9fa;
				padding: 1rem 0;
				margin-bottom: 2rem;
				border-bottom: 1px solid #dee2e6;
			}
			.settings-form {
				background: white;
				padding: 2rem;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
				max-width: 600px;
				margin: 0 auto;
			}
			.form-label {
				font-weight: 500;
			}
			.alert {
				display: none;
				margin-bottom: 1rem;
			}
			.toast {
				position: fixed;
				top: 1rem;
				right: 1rem;
				min-width: 250px;
			}
		</style>
	</head>
	<body>
		<header class="admin-header">
			<div class="container">
				<div class="d-flex justify-content-between align-items-center">
					<h1 class="h4 mb-0">DrxDion Admin</h1>
					<div>
						<a
							href="/admin/orders"
							class="btn btn-outline-primary me-2"
							>Orders</a
						>
						<a
							href="/admin/settings"
							class="btn btn-outline-secondary me-2 active"
							>Settings</a
						>
						<a href="/admin/logout" class="btn btn-outline-danger"
							>Logout</a
						>
					</div>
				</div>
			</div>
		</header>

		<div class="container">
			<div class="settings-form">
				<h2 class="mb-4">Admin Settings</h2>

				<div class="alert alert-success" role="alert" id="successAlert">
					Settings updated successfully!
				</div>

				<div
					class="alert alert-danger"
					role="alert"
					id="errorAlert"
				></div>

				<form id="settingsForm">
					<input
						type="hidden"
						name="_csrf"
						value="<%= csrfToken %>"
					/>

					<div class="mb-3">
						<label for="newUsername" class="form-label"
							>Username</label
						>
						<input
							type="text"
							class="form-control"
							id="newUsername"
							name="newUsername"
							value="<%= username %>"
							required
							pattern="[a-zA-Z0-9_-]{3,16}"
							title="Username must be 3-16 characters and can only contain letters, numbers, underscore and hyphen"
						/>
					</div>

					<div class="mb-3">
						<label for="newEmail" class="form-label">Email</label>
						<input
							type="email"
							class="form-control"
							id="newEmail"
							name="newEmail"
							value="<%= email %>"
							required
						/>
					</div>

					<div class="mb-3">
						<label for="currentPassword" class="form-label"
							>Current Password*</label
						>
						<input
							type="password"
							class="form-control"
							id="currentPassword"
							name="currentPassword"
							required
						/>
					</div>

					<div class="mb-3">
						<label for="newPassword" class="form-label"
							>New Password</label
						>
						<input
							type="password"
							class="form-control"
							id="newPassword"
							name="newPassword"
						/>
						<small class="text-muted"
							>Leave blank to keep current password</small
						>
					</div>

					<button type="submit" class="btn btn-primary">
						Update Settings
					</button>
				</form>
			</div>
		</div>

		<script src="/assets/js/bootstrap.bundle.min.js"></script>
		<script>
			document
				.getElementById("settingsForm")
				.addEventListener("submit", async (e) => {
					e.preventDefault();

					// Hide any visible alerts
					document.getElementById("successAlert").style.display =
						"none";
					document.getElementById("errorAlert").style.display =
						"none";

					const formData = {
						currentPassword:
							document.getElementById("currentPassword").value,
						newUsername:
							document.getElementById("newUsername").value,
						newEmail: document.getElementById("newEmail").value,
						newPassword:
							document.getElementById("newPassword").value ||
							undefined,
					};

					const csrfToken = document.querySelector(
						'input[name="_csrf"]'
					).value;

					try {
						const response = await fetch("/admin/settings/update", {
							method: "POST",
							headers: {
								"Content-Type": "application/json",
								"CSRF-Token": csrfToken,
							},
							body: JSON.stringify(formData),
						});

						const data = await response.json();

						if (data.success) {
							document.getElementById(
								"successAlert"
							).style.display = "block";
							document.getElementById("currentPassword").value =
								"";
							document.getElementById("newPassword").value = "";

							// If username changed, reload after a delay
							if (formData.newUsername !== "<%= username %>") {
								setTimeout(
									() => window.location.reload(),
									1000
								);
							}
						} else {
							throw new Error(data.message || "Update failed");
						}
					} catch (error) {
						const errorAlert =
							document.getElementById("errorAlert");
						errorAlert.textContent =
							error.message ||
							"An error occurred while updating settings";
						errorAlert.style.display = "block";
					}
				});
		</script>
	</body>
</html>
