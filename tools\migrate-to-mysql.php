<?php

require_once 'config.php';

try {
    $dsn = 'mysql:host=localhost;dbname=drxdion;charset=utf8mb4';
    $pdo = new PDO($dsn, 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "Starting data migration...\n";

    // Clear existing data
    $pdo->exec('DELETE FROM admin_users');
    $pdo->exec('DELETE FROM orders');
    echo "Cleared existing data\n";

    // Migrate admin users
    if (file_exists('data/admins.json')) {
        $adminsJson = file_get_contents('data/admins.json');
        $admins = json_decode($adminsJson, true);

        if ($admins) {
            $stmt = $pdo->prepare('INSERT INTO admin_users (username, password_hash, email, created_at, updated_at) VALUES (?, ?, ?, ?, ?)');

            foreach ($admins as $admin) {
                $stmt->execute([
                    $admin['username'],
                    $admin['password'],
                    $admin['email'],
                    $admin['created_at'] ?? date('Y-m-d H:i:s'),
                    $admin['updated_at'] ?? date('Y-m-d H:i:s')
                ]);
            }

            echo "Migrated " . count($admins) . " admin users\n";
        }
    }

    // Migrate orders
    if (file_exists('data/orders.json')) {
        $ordersJson = file_get_contents('data/orders.json');
        $orders = json_decode($ordersJson, true);

        if ($orders) {
            $stmt = $pdo->prepare('
                INSERT INTO orders (
                    id, customer_name, customer_email, customer_phone, 
                    shipping_address, total_amount, shipping_cost, items, 
                    status, payment_status, payment_id, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ');

            foreach ($orders as $order) {
                // Convert old format to new format
                $customerPhone = $order['customer_phone'] ??
                    $order['phone'] ??
                    '555-0000';

                $shippingAddress = $order['shipping_address'] ??
                    $order['address'] ??
                    'No address provided';

                $totalAmount = $order['total_amount'] ??
                    $order['total_price'] ??
                    $order['price'] ?? 0;

                $shippingCost = $order['shipping_cost'] ?? 0;

                // Create items array from product info
                $items = [];
                if (isset($order['product_name'])) {
                    $items[] = [
                        'name' => $order['product_name'],
                        'price' => $order['price'] ?? 0,
                        'quantity' => 1
                    ];
                } else if (isset($order['items'])) {
                    $items = $order['items'];
                }

                $status = $order['status'] ?? 'pending';
                $paymentStatus = $order['payment_status'] ?? 'pending';

                $stmt->execute([
                    $order['id'],
                    $order['customer_name'],
                    $order['customer_email'],
                    $customerPhone,
                    $shippingAddress,
                    $totalAmount,
                    $shippingCost,
                    json_encode($items),
                    $status,
                    $paymentStatus,
                    $order['payment_id'] ?? null,
                    $order['created_at'] ?? date('Y-m-d H:i:s'),
                    $order['updated_at'] ?? date('Y-m-d H:i:s')
                ]);
            }

            echo "Migrated " . count($orders) . " orders\n";
        }
    }

    // Check results
    $adminCount = $pdo->query('SELECT COUNT(*) FROM admin_users')->fetchColumn();
    $orderCount = $pdo->query('SELECT COUNT(*) FROM orders')->fetchColumn();

    echo "Migration complete! Admins: $adminCount, Orders: $orderCount\n";

    // Show some sample data
    echo "\nSample admin users:\n";
    $admins = $pdo->query('SELECT username, email FROM admin_users LIMIT 3')->fetchAll();
    foreach ($admins as $admin) {
        echo "- {$admin['username']} ({$admin['email']})\n";
    }

    echo "\nSample orders:\n";
    $orders = $pdo->query('SELECT id, customer_name, total_amount, status FROM orders LIMIT 3')->fetchAll();
    foreach ($orders as $order) {
        echo "- {$order['id']}: {$order['customer_name']} - {$order['total_amount']} TL ({$order['status']})\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
