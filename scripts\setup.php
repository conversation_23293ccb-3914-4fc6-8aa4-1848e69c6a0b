<?php

declare(strict_types=1);

/**
 * DrxDion Setup and Startup Script
 * Complete setup for the PHP conversion
 */

echo "🚀 DrxDion PHP Setup & Startup\n";
echo "==============================\n\n";

// Check PHP version
if (version_compare(PHP_VERSION, '8.0.0', '<')) {
    echo "❌ PHP 8.0 or higher is required. Current version: " . PHP_VERSION . "\n";
    exit(1);
}
echo "✅ PHP version " . PHP_VERSION . " is compatible\n";

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_sqlite', 'json', 'curl', 'openssl'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    echo "❌ Missing required PHP extensions: " . implode(', ', $missingExtensions) . "\n";
    exit(1);
}
echo "✅ All required PHP extensions are loaded\n";

// Check if .env file exists
if (!file_exists(__DIR__ . '/../.env')) {
    echo "⚠️  .env file not found. Please create one from .env.example\n";
    if (file_exists(__DIR__ . '/../.env.example')) {
        echo "📄 Copying .env.example to .env...\n";
        copy(__DIR__ . '/../.env.example', __DIR__ . '/../.env');
        echo "✅ .env file created. Please edit it with your configuration.\n";
    }
} else {
    echo "✅ .env file exists\n";
}

// Load the application
require_once __DIR__ . '/../config.php';

try {
    // Test database connection
    $database = $container->get(\DrxDion\Infrastructure\Persistence\SQLite\DatabaseConnection::class);
    echo "✅ Database connection successful\n";

    // Check if admin user exists
    $adminRepo = $container->get(\DrxDion\Domain\Contracts\AdminRepositoryInterface::class);
    if (!$adminRepo->hasAnyAdmins()) {
        echo "⚠️  No admin users found. Creating default admin...\n";

        $username = 'admin';
        $password = bin2hex(random_bytes(8));
        $email = '<EMAIL>';

        $admin = $adminRepo->createDefaultAdmin($username, $password, $email);

        echo "✅ Default admin created:\n";
        echo "   Username: $username\n";
        echo "   Password: $password\n";
        echo "   Email: $email\n";
        echo "⚠️  Please change this password after first login!\n";
    } else {
        echo "✅ Admin users exist\n";
    }
} catch (\Exception $e) {
    echo "❌ Setup error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 Setup completed successfully!\n\n";

echo "📝 Next steps:\n";
echo "1. Edit your .env file with proper configuration\n";
echo "2. Run: php scripts/check-env.php\n";
echo "3. Start development server: php -S localhost:8000 -t . index.php\n";
echo "4. Visit: http://localhost:8000\n";
echo "5. Admin panel: http://localhost:8000/admin/login\n\n";

echo "🔧 Available commands:\n";
echo "- php scripts/check-env.php     # Check environment configuration\n";
echo "- php scripts/clean-orders.php  # Clean all orders (development)\n";
echo "- php scripts/generate-secrets.php # Generate new secrets\n";
echo "- php -S localhost:8000 -t . index.php # Start development server\n\n";

echo "📚 For more information, see README-PHP.md\n";
