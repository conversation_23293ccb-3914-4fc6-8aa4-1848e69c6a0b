<?php

/**
 * Admin User Setup
 * Kullanım: php setup-admin.php
 * Not: <PERSON><PERSON><PERSON> sonrası <PERSON> silin (rm setup-admin.php)
 */

require_once 'config.php';

try {
    // Get the admin repository
    $adminRepo = $container->get('DrxDion\Domain\Contracts\AdminRepositoryInterface');

    // Check if any admin users exist
    if ($adminRepo->hasAnyAdmins()) {
        echo "Admin users already exist. Current admins:\n";

        // Try to find the default admin
        $admin = $adminRepo->findByUsername('admin');
        if ($admin) {
            echo "Username: admin\n";
            echo "Email: " . $admin->getEmail() . "\n";
            echo "Created: " . $admin->getCreatedAt()->format('Y-m-d H:i:s') . "\n";
        }
    } else {
        echo "No admin users found. Creating default admin...\n";

        // Create default admin
        $admin = $adminRepo->createDefaultAdmin('admin', 'admin123', '<EMAIL>');

        echo "Default admin created successfully!\n";
        echo "Username: admin\n";
        echo "Password: admin123\n";
        echo "Email: <EMAIL>\n";
        echo "ID: " . $admin->getId() . "\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
