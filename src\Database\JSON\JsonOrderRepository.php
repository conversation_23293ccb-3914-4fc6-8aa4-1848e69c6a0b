<?php

declare(strict_types=1);

namespace DrxDion\Database\JSON;

use DrxDion\Interfaces\OrderRepositoryInterface;
use DrxDion\Models\Order;

/**
 * File-based Order Repository
 * Fallback implementation when SQLite is not available
 */
final class JsonOrderRepository implements OrderRepositoryInterface
{
    private string $dataPath;

    public function __construct()
    {
        $this->dataPath = dirname(__DIR__, 4) . '/data';
        if (!is_dir($this->dataPath)) {
            mkdir($this->dataPath, 0755, true);
        }
    }

    public function save(Order $order): void
    {
        $orders = $this->loadOrders();
        $orders[$order->getId()] = $this->orderToArray($order);
        $this->saveOrders($orders);
    }

    public function findById(string $id): ?Order
    {
        $orders = $this->loadOrders();
        return isset($orders[$id]) ? $this->arrayToOrder($orders[$id]) : null;
    }
    public function findAll(int $limit = 100, int $offset = 0): array
    {
        $orders = $this->loadOrders();
        $allOrders = array_map([$this, 'arrayToOrder'], $orders);

        // Apply pagination
        return array_slice($allOrders, $offset, $limit);
    }

    public function updatePayment(string $id, ?string $paymentId, string $status): bool
    {
        $orders = $this->loadOrders();
        if (!isset($orders[$id])) {
            return false;
        }

        $orders[$id]['payment_id'] = $paymentId;
        $orders[$id]['payment_status'] = $status;
        $orders[$id]['updated_at'] = date('Y-m-d H:i:s');
        $this->saveOrders($orders);
        return true;
    }

    public function count(): int
    {
        $orders = $this->loadOrders();
        return count($orders);
    }

    public function search(array $criteria, int $limit = 100, int $offset = 0): array
    {
        $orders = $this->loadOrders();
        $filtered = [];

        foreach ($orders as $order) {
            $matches = true;

            // Search by customer name/email/order ID
            if (!empty($criteria['customer_name'])) {
                $searchTerm = strtolower($criteria['customer_name']);
                $customerName = strtolower($order['customer_name'] ?? '');
                $customerEmail = strtolower($order['customer_email'] ?? '');
                $orderId = strtolower($order['id'] ?? '');

                if (
                    strpos($customerName, $searchTerm) === false &&
                    strpos($customerEmail, $searchTerm) === false &&
                    strpos($orderId, $searchTerm) === false
                ) {
                    $matches = false;
                }
            }

            // Filter by payment status
            if (
                !empty($criteria['payment_status']) &&
                ($order['payment_status'] ?? '') !== $criteria['payment_status']
            ) {
                $matches = false;
            }

            // Filter by payment method
            if (
                !empty($criteria['payment_method']) &&
                ($order['payment_method'] ?? '') !== $criteria['payment_method']
            ) {
                $matches = false;
            }

            // Filter by date range
            if (!empty($criteria['date_from'])) {
                $orderDate = strtotime($order['created_at'] ?? '');
                $fromDate = strtotime($criteria['date_from']);
                if ($orderDate < $fromDate) {
                    $matches = false;
                }
            }

            if (!empty($criteria['date_to'])) {
                $orderDate = strtotime($order['created_at'] ?? '');
                $toDate = strtotime($criteria['date_to'] . ' 23:59:59');
                if ($orderDate > $toDate) {
                    $matches = false;
                }
            }

            if ($matches) {
                $filtered[] = $this->arrayToOrder($order);
            }
        }

        return array_slice($filtered, $offset, $limit);
    }

    public function countWithCriteria(array $criteria): int
    {
        $orders = $this->loadOrders();
        $count = 0;

        foreach ($orders as $order) {
            $matches = true;

            // Search by customer name/email/order ID
            if (!empty($criteria['customer_name'])) {
                $searchTerm = strtolower($criteria['customer_name']);
                $customerName = strtolower($order['customer_name'] ?? '');
                $customerEmail = strtolower($order['customer_email'] ?? '');
                $orderId = strtolower($order['id'] ?? '');

                if (
                    strpos($customerName, $searchTerm) === false &&
                    strpos($customerEmail, $searchTerm) === false &&
                    strpos($orderId, $searchTerm) === false
                ) {
                    $matches = false;
                }
            }

            // Filter by payment status
            if (
                !empty($criteria['payment_status']) &&
                ($order['payment_status'] ?? '') !== $criteria['payment_status']
            ) {
                $matches = false;
            }

            // Filter by payment method
            if (
                !empty($criteria['payment_method']) &&
                ($order['payment_method'] ?? '') !== $criteria['payment_method']
            ) {
                $matches = false;
            }

            // Filter by date range
            if (!empty($criteria['date_from'])) {
                $orderDate = strtotime($order['created_at'] ?? '');
                $fromDate = strtotime($criteria['date_from']);
                if ($orderDate < $fromDate) {
                    $matches = false;
                }
            }

            if (!empty($criteria['date_to'])) {
                $orderDate = strtotime($order['created_at'] ?? '');
                $toDate = strtotime($criteria['date_to'] . ' 23:59:59');
                if ($orderDate > $toDate) {
                    $matches = false;
                }
            }

            if ($matches) {
                $count++;
            }
        }

        return $count;
    }

    public function findByStatus(string $status, int $limit = 100): array
    {
        $orders = $this->loadOrders();
        $filtered = [];
        $count = 0;

        foreach ($orders as $order) {
            if (($order['payment_status'] ?? 'pending') === $status) {
                $filtered[] = $this->arrayToOrder($order);
                $count++;
                if ($count >= $limit) {
                    break;
                }
            }
        }

        return $filtered;
    }

    public function countByStatus(string $status): int
    {
        $orders = $this->loadOrders();
        $count = 0;

        foreach ($orders as $order) {
            if (($order['payment_status'] ?? 'pending') === $status) {
                $count++;
            }
        }

        return $count;
    }

    public function countAll(): int
    {
        return $this->count();
    }

    public function updateStatus(string $orderId, string $status): bool
    {
        $orders = $this->loadOrders();
        if (!isset($orders[$orderId])) {
            return false;
        }

        $orders[$orderId]['payment_status'] = $status;
        $orders[$orderId]['updated_at'] = date('Y-m-d H:i:s');
        $this->saveOrders($orders);
        return true;
    }

    public function delete(string $orderId): bool
    {
        $orders = $this->loadOrders();
        if (!isset($orders[$orderId])) {
            return false;
        }

        unset($orders[$orderId]);
        $this->saveOrders($orders);
        return true;
    }

    public function clear(): bool
    {
        try {
            $this->saveOrders([]);
            return true;
        } catch (\Exception $e) {
            error_log("JSON Repository clear error: " . $e->getMessage());
            return false;
        }
    }

    private function loadOrders(): array
    {
        $filePath = $this->dataPath . '/orders.json';
        if (!file_exists($filePath)) {
            return [];
        }

        $data = file_get_contents($filePath);
        return $data ? json_decode($data, true) : [];
    }

    private function saveOrders(array $orders): void
    {
        $filePath = $this->dataPath . '/orders.json';
        file_put_contents($filePath, json_encode($orders, JSON_PRETTY_PRINT));
    }

    private function orderToArray(Order $order): array
    {
        return [
            'id' => $order->getId(),
            'product_name' => $order->getProductName(),
            'price' => $order->getPrice(),
            'total_price' => $order->getTotalPrice(),
            'shipping_cost' => $order->getShippingCost(),
            'payment_method' => $order->getPaymentMethod(),
            'payment_status' => $order->getPaymentStatus(),
            'payment_id' => $order->getPaymentId(),
            'customer_name' => $order->getCustomerName(),
            'customer_email' => $order->getCustomerEmail(),
            'shipping_address' => $order->getShippingAddress(),
            'created_at' => $order->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $order->getUpdatedAt()->format('Y-m-d H:i:s')
        ];
    }

    private function arrayToOrder(array $data): Order
    {
        return new Order(
            $data['id'],
            $data['product_name'],
            (float)$data['price'],
            (float)$data['total_price'],
            (float)($data['shipping_cost'] ?? 0),
            $data['payment_method'],
            $data['payment_status'] ?? 'pending',
            $data['payment_id'] ?? '',
            $data['customer_name'] ?? '',
            $data['customer_email'] ?? '',
            $data['shipping_address'],
            new \DateTimeImmutable($data['created_at']),
            new \DateTimeImmutable($data['updated_at'])
        );
    }
}
