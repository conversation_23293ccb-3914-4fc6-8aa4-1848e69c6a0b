<?php

declare(strict_types=1);

/**
 * Laravel-style Application Bootstrap
 * DrxDion E-commerce Platform
 */

// Define base path
define('BASE_PATH', dirname(__DIR__));

// Composer autoloader
if (file_exists(BASE_PATH . '/vendor/autoload.php')) {
    require_once BASE_PATH . '/vendor/autoload.php';
}

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $envContent = file_get_contents(BASE_PATH . '/.env');
    $envLines = explode("\n", $envContent);
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || str_starts_with($line, '#')) {
            continue;
        }
        if (str_contains($line, '=')) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Simple service container
class Container
{
    private static array $services = [];

    public static function bind(string $abstract, callable $concrete): void
    {
        self::$services[$abstract] = $concrete;
    }

    public static function resolve(string $abstract): mixed
    {
        if (isset(self::$services[$abstract])) {
            return self::$services[$abstract]();
        }

        // Try to auto-resolve
        if (class_exists($abstract)) {
            return new $abstract();
        }

        throw new Exception("Service {$abstract} not found");
    }
}

// Register services
Container::bind('App\Services\StorageService', function () {
    return new App\Services\StorageService();
});

Container::bind('App\Http\Controllers\AdminController', function () {
    $storageService = Container::resolve('App\Services\StorageService');
    return new App\Http\Controllers\AdminController($storageService);
});

Container::bind('App\Http\Controllers\ApiController', function () {
    $adminController = Container::resolve('App\Http\Controllers\AdminController');
    $storageService = Container::resolve('App\Services\StorageService');
    return new App\Http\Controllers\ApiController($adminController, $storageService);
});

Container::bind('App\Http\Controllers\PageController', function () {
    return new App\Http\Controllers\PageController();
});

// Start session for admin authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set timezone
date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'UTC');

// Error reporting - simplified for stability
// Skip error reporting calls that cause issues

return Container::class;
