<?php

declare(strict_types=1);

// Simple test to debug the admin controller instantiation
require_once 'vendor/autoload.php';

try {
    echo "1. Loading config...\n";
    require_once 'config.php';

    echo "2. Creating container...\n";
    $container = new \DrxDion\Config\Container();

    echo "3. Registering services...\n";
    require_once 'src/Config/services.php';
    registerServices($container);

    echo "4. Getting AdminController...\n";
    $adminController = $container->get(\DrxDion\Controllers\AdminController::class);

    echo "5. SUCCESS: AdminController created!\n";
} catch (\Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (\Error $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
