document.addEventListener("DOMContentLoaded", function () {
	// Initialize CSRF token on page load
	initializeCSRFToken();

	// Get DOM elements with null checks
	const paymentModal = document.getElementById("paymentModal");
	const modalProductName = document.getElementById("modalProductName");
	const modalProductPrice = document.getElementById("modalProductPrice");
	const modalTotalPrice = document.getElementById("modalTotalPrice");
	const modalShippingCost = document.getElementById("modalShippingCost");
	const modalShippingText = document.getElementById("modalShippingText");
	const completePaymentBtn = document.getElementById("completePayment");
	const customerAddress = document.getElementById("customerAddress");
	const paymentOptions = document.querySelectorAll(".payment-option");
	let currentPaymentMethod = "cod"; // Default to Cash on Delivery
	let currentPaymentLink = ""; // Store payment link for current product
	let currentShippingCost = 10; // Default shipping cost

	// Check if essential elements exist
	if (!paymentModal) {
		console.error("Payment modal element not found!");
		return;
	}
	if (
		!modalProductName ||
		!modalProductPrice ||
		!modalTotalPrice ||
		!modalShippingCost
	) {
		console.error("Modal product elements not found!");
		return;
	}

	if (!completePaymentBtn) {
		console.error("Complete payment button not found!");
		return;
	}

	if (!customerAddress) {
		console.error("Customer address element not found!");
		return;
	}

	// Initialize Bootstrap modal
	const bsModal = new bootstrap.Modal(paymentModal);

	// Clear errors when modal is closed
	paymentModal.addEventListener("hidden.bs.modal", function () {
		clearAddressError();
		if (customerAddress) {
			customerAddress.value = "";
		}
	});
	// Add input event listener to address field to clear errors when user types
	if (customerAddress) {
		customerAddress.addEventListener("input", function () {
			if (this.value.trim()) {
				clearAddressError();
			}
		});
	}

	// Initialize CSRF token
	async function initializeCSRFToken() {
		try {
			const response = await fetch('/api/csrf-token');
			if (response.ok) {
				const data = await response.json();
				// Update meta tag
				const metaTag = document.querySelector('meta[name="csrf-token"]');
				if (metaTag) {
					metaTag.setAttribute('content', data.token);
				}
				console.log('CSRF token initialized successfully');
			} else {
				console.warn('Failed to fetch CSRF token');
			}
		} catch (error) {
			console.error('Error fetching CSRF token:', error);
		}
	}

	// Add click event listeners to payment options
	paymentOptions.forEach((option) => {
		option.addEventListener("click", function () {
			const radio = this.querySelector('input[type="radio"]');
			if (radio) {
				currentPaymentMethod = radio.value;
				updatePaymentSelection();
			}
		});
	});
	function updatePaymentSelection() {
		// Update payment option visuals
		paymentOptions.forEach((option) => {
			const radio = option.querySelector('input[type="radio"]');
			if (radio && radio.value === currentPaymentMethod) {
				option.classList.add("selected");
				radio.checked = true;
			} else {
				option.classList.remove("selected");
			}
		});
		// Update payment info text visibility and calculate total
		const cardText = document.querySelector(".credit-card-text");
		const codText = document.querySelector(".cash-on-delivery-text");

		if (cardText && codText) {
			if (currentPaymentMethod === "card") {
				cardText.classList.remove("d-none");
				codText.classList.add("d-none");
				if (completePaymentBtn)
					completePaymentBtn.textContent = "Proceed to Payment";
			} else {
				cardText.classList.add("d-none");
				codText.classList.remove("d-none");
				if (completePaymentBtn)
					completePaymentBtn.textContent = "Place Order";
			}
		}

		// Update total price based on payment method
		updateTotalPrice();
	}
	// Function to update total price based on payment method
	function updateTotalPrice() {
		if (
			!modalProductPrice ||
			!modalTotalPrice ||
			!modalShippingCost ||
			!modalShippingText
		)
			return;

		const productPriceText = modalProductPrice.textContent.replace("$", "");
		const productPrice = parseFloat(productPriceText) || 0;

		let shippingCost = 0;
		let totalPrice = productPrice;
		let shippingText = "";

		// Apply shipping cost based on payment method
		if (currentPaymentMethod === "card") {
			// Card payment: shipping cost applies
			shippingCost = currentShippingCost;
			totalPrice = productPrice + shippingCost;
			shippingText = `$${shippingCost.toFixed(2)}`;
		} else {
			// Cash on delivery: free shipping
			shippingCost = 0;
			totalPrice = productPrice;
			shippingText = "Free";
		}

		// Update display
		modalShippingCost.textContent = `${shippingCost.toFixed(2)}`;
		modalShippingText.textContent = shippingText;
		modalTotalPrice.textContent = `$${totalPrice.toFixed(2)}`;
	}

	// Add click event listeners to all "Buy Now" buttons
	document.querySelectorAll(".btn-buy").forEach((button) => {
		button.addEventListener("click", function () {
			// Get product details from data attributes
			const productName = this.getAttribute("data-product-name");
			const productPrice = parseFloat(
				this.getAttribute("data-product-price")
			);
			const shippingCost = parseFloat(
				this.getAttribute("data-shipping-cost") || "10"
			);
			const paymentLink = this.getAttribute("data-payment-link");

			// Store payment link and shipping cost for later use
			currentPaymentLink = paymentLink;
			currentShippingCost = shippingCost;

			// Update modal content
			if (modalProductName) modalProductName.textContent = productName;
			if (modalProductPrice)
				modalProductPrice.textContent = `$${productPrice.toFixed(2)}`;
			if (modalShippingCost)
				modalShippingCost.textContent = `${shippingCost.toFixed(2)}`;
			if (modalTotalPrice)
				modalTotalPrice.textContent = `$${(
					productPrice + shippingCost
				).toFixed(2)}`;

			// Reset form and payment selection
			if (customerAddress) customerAddress.value = "";

			// Reset to default payment method (Cash on Delivery)
			currentPaymentMethod = "cod";
			const paymentRadios = document.querySelectorAll(
				'input[name="paymentMethod"]'
			);
			paymentRadios.forEach((radio) => {
				radio.checked = radio.value === "cod";
			});
			updatePaymentSelection();

			// Show modal
			bsModal.show();
		});
	}); // Show error toast
	function showErrorToast(message) {
		// Create toast container if it doesn't exist
		let toastContainer = document.getElementById("toast-container");
		if (!toastContainer) {
			toastContainer = document.createElement("div");
			toastContainer.id = "toast-container";
			toastContainer.className =
				"toast-container position-fixed top-0 end-0 p-3";
			toastContainer.style.zIndex = "9999";
			document.body.appendChild(toastContainer);
		}

		const toastHtml = `
			<div class="toast align-items-center text-bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
				<div class="d-flex">
					<div class="toast-body">
						<i class="fas fa-exclamation-circle me-2"></i>
						${message}
					</div>
					<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
				</div>
			</div>
		`;

		toastContainer.insertAdjacentHTML("beforeend", toastHtml);
		const toastElement = toastContainer.lastElementChild;
		const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
		toast.show();

		// Remove toast element after it's hidden
		toastElement.addEventListener("hidden.bs.toast", function () {
			this.remove();
		});
	}

	// Show address validation error
	function showAddressError(message) {
		if (!customerAddress) return;

		// Add error styling to textarea
		customerAddress.classList.add("is-invalid");

		// Create or update error message
		let errorDiv = document.getElementById("address-error");
		if (!errorDiv) {
			errorDiv = document.createElement("div");
			errorDiv.id = "address-error";
			errorDiv.className = "invalid-feedback";
			customerAddress.parentNode.appendChild(errorDiv);
		}
		errorDiv.textContent = message;
	}

	// Clear address validation error
	function clearAddressError() {
		if (!customerAddress) return;

		customerAddress.classList.remove("is-invalid");
		const errorDiv = document.getElementById("address-error");
		if (errorDiv) {
			errorDiv.remove();
		}
	}

	// Form validation
	function validateForm() {
		const address = customerAddress ? customerAddress.value.trim() : "";

		if (!address) {
			showAddressError("Please enter your shipping address.");
			return false;
		}

		clearAddressError();
		return true;
	}

	// Complete payment button click
	completePaymentBtn.addEventListener("click", async function () {
		if (!validateForm()) {
			return;
		}
		const productName = modalProductName
			? modalProductName.textContent
			: "Unknown Product";
		const price = modalProductPrice
			? parseFloat(modalProductPrice.textContent.replace("$", ""))
			: 0;
		const totalPrice = modalTotalPrice
			? parseFloat(modalTotalPrice.textContent.replace("$", ""))
			: 0;
		const shippingAddress = customerAddress
			? customerAddress.value.trim()
			: "";

		// Use default values for customer info since fields are removed
		const customerName = "Anonymous";
		const customerEmail = "<EMAIL>";		try {
			// Get CSRF token from meta tag first, then cookie as fallback
			function getCSRFToken() {
				// Try meta tag first
				const metaTag = document.querySelector('meta[name="csrf-token"]');
				if (metaTag && metaTag.getAttribute('content')) {
					return metaTag.getAttribute('content');
				}
				
				// Fallback to cookie
				const value = `; ${document.cookie}`;
				const parts = value.split(`; csrf-token=`);
				if (parts.length === 2) {
					return parts.pop().split(";").shift();
				}
				
				return null;
			}

			const csrfToken = getCSRFToken();
			console.log("CSRF Token found:", csrfToken ? "Yes" : "No"); // Debug log

			if (!csrfToken) {
				throw new Error(
					"CSRF token not found. Please refresh the page and try again."
				);
			}

			// Create order
			const orderResponse = await fetch("/api/create-order", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					"X-CSRF-Token": csrfToken,
				},
				body: JSON.stringify({
					productName,
					price,
					totalPrice,
					shippingCost: currentShippingCost,
					paymentMethod: currentPaymentMethod,
					shippingAddress,
					customerName,
					customerEmail,
				}),
			});

			if (!orderResponse.ok) {
				throw new Error("Failed to create order");
			}

			const orderData = await orderResponse.json(); // Handle response based on payment method
			if (currentPaymentMethod === "card") {
				// Use data-payment-link if available, otherwise use server-generated payment URL
				if (currentPaymentLink) {
					console.log(
						"Redirecting to product payment link:",
						currentPaymentLink
					);
					window.location.href = currentPaymentLink;
				} else {
					console.log(
						"Using server-generated payment URL:",
						orderData.paymentUrl
					);
					window.location.href = orderData.paymentUrl;
				}
			} else {
				// Show order confirmation
				window.location.href = `/order-summary?orderId=${orderData.orderId}`;
			}
		} catch (error) {
			console.error("Error:", error);
			showErrorToast(
				"An error occurred while processing your order. Please try again."
			);
		}
	});
});
