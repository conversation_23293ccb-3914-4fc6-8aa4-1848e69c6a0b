<?php

declare(strict_types=1);

namespace DrxDion\Controllers;

use DrxDion\Database\MySQL\MySQLSettingsRepository;
use DrxDion\Services\StorageService;
use DrxDion\Database\MySQL\MySQLAdminRepository;

/**
 * Admin Controller
 * Handles admin panel API endpoints
 */
final class AdminController
{
    public function __construct(
        private readonly MySQLSettingsRepository $settingsRepository,
        private readonly StorageService $storageService,
        private readonly MySQLAdminRepository $adminRepository
    ) {}

    public function getSettings(array $request): void
    {
        header('Content-Type: application/json');

        try {
            $settings = $this->settingsRepository->getAll();

            // Convert to simple key-value format for frontend
            $settingsData = [];
            foreach ($settings as $setting) {
                $settingsData[$setting->getKey()] = $setting->getValue();
            }

            // Provide default values if settings don't exist
            $defaultSettings = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'notification_email' => '<EMAIL>',
                'site_name' => 'DrxDion',
                'timezone' => 'Europe/Istanbul'
            ];

            $result = array_merge($defaultSettings, $settingsData);

            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to load settings: ' . $e->getMessage()
            ]);
        }
    }

    public function updateSettings(array $request): void
    {
        header('Content-Type: application/json');

        try {
            $data = $request['body'] ?? [];

            if (empty($data)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'No data provided'
                ]);
                return;
            }

            // Handle password update separately if provided
            if (!empty($data['new_password'])) {
                if (empty($data['current_password'])) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'Current password is required to change password'
                    ]);
                    return;
                }

                // Verify current password
                $currentUser = $this->adminRepository->findByUsername($data['username'] ?? 'admin');
                if (!$currentUser || !password_verify($data['current_password'], $currentUser->getPasswordHash())) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'Current password is incorrect'
                    ]);
                    return;
                }

                // Update password
                $hashedPassword = password_hash($data['new_password'], PASSWORD_DEFAULT);
                $this->adminRepository->updatePassword($currentUser->getId(), $hashedPassword);
            }

            // Update other settings
            $settingsToUpdate = [];
            $allowedSettings = ['username', 'email', 'notification_email', 'site_name', 'timezone'];

            foreach ($allowedSettings as $key) {
                if (isset($data[$key])) {
                    $settingsToUpdate[$key] = $data[$key];
                }
            }

            if (!empty($settingsToUpdate)) {
                $this->settingsRepository->updateMultiple($settingsToUpdate);
            }

            echo json_encode([
                'success' => true,
                'message' => 'Settings updated successfully'
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to update settings: ' . $e->getMessage()
            ]);
        }
    }

    public function getStorageStatus(array $request): void
    {
        header('Content-Type: application/json');

        try {
            $status = $this->storageService->getStatus();
            $healthMetrics = $this->storageService->getHealthMetrics();
            $syncStats = $this->storageService->getSyncStats();

            echo json_encode([
                'success' => true,
                'data' => [
                    'status' => $status,
                    'health' => $healthMetrics,
                    'sync' => $syncStats
                ]
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to get storage status: ' . $e->getMessage()
            ]);
        }
    }

    public function syncStorage(array $request): void
    {
        header('Content-Type: application/json');

        try {
            // CSRF protection
            if (!$this->validateCSRF($request)) {
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid CSRF token'
                ]);
                return;
            }

            $data = $request['body'] ?? [];
            $forceReplace = ($data['force_replace'] ?? false) === true;

            $result = $this->storageService->sync($forceReplace);

            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Storage sync completed successfully',
                    'data' => $result
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => $result['error'] ?? 'Sync failed',
                    'data' => $result
                ]);
            }
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to sync storage: ' . $e->getMessage()
            ]);
        }
    }

    private function validateCSRF(array $request): bool
    {
        $headers = $request['headers'] ?? [];
        $providedToken = $headers['X-CSRF-Token'] ?? $headers['x-csrf-token'] ?? '';
        $sessionToken = $_SESSION['csrf_token'] ?? '';

        return !empty($providedToken) && !empty($sessionToken) && hash_equals($sessionToken, $providedToken);
    }
}
