<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta
			http-equiv="Content-Security-Policy"
			content="default-src 'self' https: http:; connect-src 'self' https: http: localhost:*; script-src 'self' 'unsafe-inline' https: http:; style-src 'self' 'unsafe-inline' https: http:; img-src 'self' data: https: http:; font-src 'self' data: https: http:;"
		/>
		<title>Admin Settings - DrxDion</title>
		<link href="/assets/css/bootstrap.min.css" rel="stylesheet" />
		<link href="/assets/css/bootstrap-icons.css" rel="stylesheet" />
		<style>
			:root {
				--primary-color: #6366f1;
				--secondary-color: #f8fafc;
				--success-color: #10b981;
				--warning-color: #f59e0b;
				--danger-color: #ef4444;
				--gray-50: #f9fafb;
				--gray-100: #f3f4f6;
				--gray-200: #e5e7eb;
				--gray-300: #d1d5db;
				--gray-600: #4b5563;
				--gray-800: #1f2937;
				--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
				--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
					0 2px 4px -1px rgba(0, 0, 0, 0.06);
				--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
					0 4px 6px -2px rgba(0, 0, 0, 0.05);
			}
			body {
				background: linear-gradient(
					135deg,
					var(--gray-50) 0%,
					var(--gray-100) 100%
				);
				font-family: "Inter", -apple-system, BlinkMacSystemFont,
					"Segoe UI", Roboto, sans-serif;
				min-height: 100vh;
			}
			.navbar {
				background: linear-gradient(
					135deg,
					var(--primary-color) 0%,
					#4f46e5 100%
				) !important;
				box-shadow: var(--shadow-lg);
				border: none;
			}
			.navbar-brand {
				font-weight: 700;
				font-size: 1.5rem;
				color: white !important;
			}
			.settings-container {
				padding: 2.5rem 1rem;
				max-width: 1000px;
			}
			.settings-header {
				background: linear-gradient(
					135deg,
					white 0%,
					var(--gray-50) 100%
				);
				border-radius: 20px;
				padding: 2.5rem;
				margin-bottom: 2rem;
				box-shadow: var(--shadow-lg);
				border: 1px solid var(--gray-200);
				position: relative;
				overflow: hidden;
			}
			.settings-header::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 4px;
				background: linear-gradient(
					90deg,
					var(--primary-color),
					#4f46e5,
					var(--success-color)
				);
			}
			.settings-header h2 {
				font-weight: 800;
				color: var(--gray-800);
				margin: 0 0 1rem 0;
				display: flex;
				align-items: center;
				gap: 0.75rem;
				font-size: 2rem;
			}
			.settings-header h2 i {
				background: linear-gradient(
					135deg,
					var(--primary-color),
					#4f46e5
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}
			.settings-header p {
				color: var(--gray-600);
				margin: 0;
				font-size: 1.1rem;
			}
			.settings-card {
				background: white;
				border-radius: 16px;
				margin-bottom: 2rem;
				border: 1px solid var(--gray-200);
				overflow: hidden;
				box-shadow: var(--shadow-sm);
				transition: all 0.3s ease;
			}
			.settings-card:hover {
				transform: translateY(-2px);
				box-shadow: var(--shadow-md);
			}
			.settings-card-header {
				background: linear-gradient(
					135deg,
					var(--gray-50) 0%,
					white 100%
				);
				padding: 1.5rem 2rem;
				border-bottom: 1px solid var(--gray-200);
				position: relative;
			}
			.settings-card-header::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 2px;
				background: linear-gradient(
					90deg,
					var(--primary-color),
					#4f46e5
				);
			}
			.settings-card-header h5 {
				font-weight: 700;
				color: var(--gray-800);
				margin: 0;
				display: flex;
				align-items: center;
				gap: 0.5rem;
				font-size: 1.1rem;
			}
			.settings-card-header i {
				color: var(--primary-color);
			}
			.settings-card-body {
				padding: 2rem;
			}
			.form-label {
				font-size: 0.875rem;
				font-weight: 600;
				color: var(--gray-700);
				margin-bottom: 0.5rem;
				text-transform: uppercase;
				letter-spacing: 0.025em;
			}
			.form-control {
				border: 2px solid var(--gray-200);
				border-radius: 12px;
				padding: 0.875rem 1rem;
				font-size: 0.95rem;
				transition: all 0.3s ease;
				background: white;
				font-weight: 500;
			}
			.form-control:focus {
				border-color: var(--primary-color);
				box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.12);
				outline: none;
				transform: translateY(-1px);
			}
			.form-control:hover {
				border-color: var(--gray-300);
			}
			.btn-save {
				background: linear-gradient(
					135deg,
					var(--primary-color) 0%,
					#4f46e5 100%
				);
				border: none;
				border-radius: 12px;
				padding: 1rem 2rem;
				color: white;
				font-size: 1rem;
				font-weight: 700;
				transition: all 0.3s ease;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 0.5rem;
				box-shadow: var(--shadow-sm);
				text-transform: uppercase;
				letter-spacing: 0.025em;
			}
			.btn-save:hover {
				transform: translateY(-2px);
				box-shadow: var(--shadow-lg);
				background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
				color: white;
			}
			.btn-save:active {
				transform: translateY(0);
			}
			.btn-save.loading {
				background: var(--gray-400);
				cursor: not-allowed;
				transform: none;
			}
			.btn-save.loading:hover {
				background: var(--gray-400);
				transform: none;
				box-shadow: var(--shadow-sm);
			}
			.save-feedback {
				display: none;
				margin-top: 1rem;
			}
			@media (max-width: 768px) {
				.settings-header {
					padding: 1.5rem;
				}
				.settings-header h2 {
					font-size: 1.5rem;
				}
				.settings-card-header {
					padding: 1rem 1.5rem;
				}
				.settings-card-body {
					padding: 1.5rem;
				}
				.btn-save {
					padding: 1rem 1.5rem;
				}
			}
		</style>
	</head>
	<body>
		<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
			<div class="container-fluid">
				<a class="navbar-brand" href="/admin/orders">DrxDion Admin</a>
				<button
					class="navbar-toggler"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#navbarNav"
				>
					<span class="navbar-toggler-icon"></span>
				</button>
				<div class="collapse navbar-collapse" id="navbarNav">
					<ul class="navbar-nav">
						<li class="nav-item">
							<a class="nav-link" href="/admin/orders">Orders</a>
						</li>
						<li class="nav-item">
							<a class="nav-link active" href="/admin/settings"
								>Settings</a
							>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="/admin/storage"
								>Storage</a
							>
						</li>
					</ul>
					<ul class="navbar-nav ms-auto">
						<li class="nav-item">
							<button
								class="btn btn-outline-light"
								onclick="logout()"
							>
								Logout
							</button>
						</li>
					</ul>
				</div>
			</div>
		</nav>

		<div class="container settings-container">
			<div class="settings-header">
				<h2>
					<i class="bi bi-gear"></i>
					Settings Management
				</h2>
				<p>Configure your admin account and system preferences</p>
			</div>

			<div class="row">
				<div class="col-12">
					<form id="settingsForm">
						<div class="settings-card">
							<div class="settings-card-header">
								<h5>
									<i class="bi bi-person-circle"></i>
									Admin Account
								</h5>
							</div>
							<div class="settings-card-body">
								<div class="mb-3">
									<label for="username" class="form-label"
										>Username</label
									>
									<input
										type="text"
										class="form-control"
										id="username"
										name="username"
										placeholder="Enter your username"
										required
									/>
								</div>
								<div class="mb-3">
									<label for="email" class="form-label"
										>Email Address</label
									>
									<input
										type="email"
										class="form-control"
										id="email"
										name="email"
										placeholder="Enter your email address"
										required
									/>
								</div>
								<div class="mb-3">
									<label
										for="currentPassword"
										class="form-label"
										>Current Password</label
									>
									<input
										type="password"
										class="form-control"
										id="currentPassword"
										name="currentPassword"
										placeholder="Enter current password to make changes"
									/>
								</div>
								<div class="mb-3">
									<label for="newPassword" class="form-label"
										>New Password</label
									>
									<input
										type="password"
										class="form-control"
										id="newPassword"
										name="newPassword"
										placeholder="Leave blank to keep current password"
									/>
									<div class="form-text text-muted mt-1">
										<small
											>Leave blank if you don't want to
											change your password</small
										>
									</div>
								</div>
								<div class="mb-0">
									<label
										for="confirmPassword"
										class="form-label"
										>Confirm New Password</label
									>
									<input
										type="password"
										class="form-control"
										id="confirmPassword"
										name="confirmPassword"
										placeholder="Confirm your new password"
									/>
								</div>
							</div>
						</div>

						<div class="settings-card">
							<div class="settings-card-header">
								<h5>
									<i class="bi bi-envelope"></i>
									Email Settings
								</h5>
							</div>
							<div class="settings-card-body">
								<div class="mb-0">
									<label
										for="notification_email"
										class="form-label"
										>Order Notification Email</label
									>
									<input
										type="email"
										class="form-control"
										id="notification_email"
										name="notification_email"
										placeholder="Email address for order notifications"
										required
									/>
									<div class="form-text text-muted mt-1">
										<small
											>This email will receive
											notifications for new orders</small
										>
									</div>
								</div>
							</div>
						</div>

						<div class="d-grid">
							<button type="submit" class="btn btn-save">
								<i class="bi bi-check-circle"></i>
								Save Settings
							</button>
						</div>

						<div id="saveFeedback" class="alert save-feedback">
							<!-- Feedback message will be inserted here -->
						</div>
					</form>
				</div>
			</div>
		</div>

		<!-- Toast Container -->
		<div class="toast-container position-fixed bottom-0 end-0 p-3">
			<div
				id="liveToast"
				class="toast"
				role="alert"
				aria-live="assertive"
				aria-atomic="true"
			>
				<div class="toast-header">
					<strong class="me-auto" id="toastTitle"
						>Notification</strong
					>
					<button
						type="button"
						class="btn-close"
						data-bs-dismiss="toast"
						aria-label="Close"
					></button>
				</div>
				<div class="toast-body" id="toastMessage">
					<!-- Message will be inserted here -->
				</div>
			</div>
		</div>

		<script src="/assets/js/bootstrap.bundle.min.js"></script>
		<script>
			// Toast notification function
			function showToast(title, message, type = "success") {
				const toastElement = document.getElementById("liveToast");
				const toastTitle = document.getElementById("toastTitle");
				const toastMessage = document.getElementById("toastMessage");
				const toastHeader = toastElement.querySelector(".toast-header");

				// Set title and message
				toastTitle.textContent = title;
				toastMessage.textContent = message;

				// Remove existing color classes and add new one
				toastHeader.classList.remove(
					"text-bg-success",
					"text-bg-danger",
					"text-bg-warning",
					"text-bg-info"
				);

				switch (type) {
					case "success":
						toastHeader.classList.add("text-bg-success");
						break;
					case "error":
					case "danger":
						toastHeader.classList.add("text-bg-danger");
						break;
					case "warning":
						toastHeader.classList.add("text-bg-warning");
						break;
					case "info":
						toastHeader.classList.add("text-bg-info");
						break;
				}

				// Show toast
				const toast = new bootstrap.Toast(toastElement);
				toast.show();
			}

			async function loadSettings() {
				const messageDiv = document.getElementById("message");
				try {
					const response = await fetch("/api/get-settings", {
						method: "GET",
						credentials: "include",
						headers: {
							Accept: "application/json",
						},
					});

					if (response.status === 401) {
						window.location.href = "/admin/login";
						return;
					}

					const response_data = await response.json();
					if (response_data.error) {
						throw new Error(response_data.error);
					}

					// Extract settings data from API response
					const data = response_data.data || response_data;

					// Fill form with current settings
					document.getElementById("username").value =
						data.username || "";
					document.getElementById("email").value = data.email || "";
					document.getElementById("notification_email").value =
						data.notification_email || data.email || "";

					// Password fields are left empty for security
					document.getElementById("currentPassword").value = "";
					document.getElementById("newPassword").value = "";
					document.getElementById("confirmPassword").value = "";
				} catch (error) {
					console.error("Error loading settings:", error);
					showToast(
						"Error",
						"Error loading settings: " + error.message,
						"error"
					);
				}
			}

			// Initialize settings load
			loadSettings();

			// Handle form submission
			document
				.getElementById("settingsForm")
				.addEventListener("submit", async function (e) {
					e.preventDefault();

					// Add loading state to button
					const submitButton = this.querySelector(
						'button[type="submit"]'
					);
					submitButton.classList.add("loading");
					submitButton.disabled = true;
					const originalText = submitButton.innerHTML;
					submitButton.innerHTML =
						'<i class="bi bi-hourglass-split"></i> Saving...';

					const newPassword =
						document.getElementById("newPassword").value;
					const confirmPassword =
						document.getElementById("confirmPassword").value;

					if (newPassword && newPassword !== confirmPassword) {
						showToast(
							"Error",
							"New passwords do not match",
							"error"
						);
						// Reset button state
						submitButton.classList.remove("loading");
						submitButton.disabled = false;
						submitButton.innerHTML = originalText;
						return;
					}

					const formData = {
						username: document.getElementById("username").value,
						email: document.getElementById("email").value,
						currentPassword:
							document.getElementById("currentPassword").value,
						newPassword: newPassword || undefined,
						notification_email:
							document.getElementById("notification_email").value,
					};

					try {
						const response = await fetch("/api/settings/update", {
							method: "POST",
							headers: {
								"Content-Type": "application/json",
							},
							credentials: "include",
							body: JSON.stringify(formData),
						});

						if (response.status === 401) {
							window.location.href = "/admin/login";
							return;
						}

						const data = await response.json();
						if (data.error) {
							throw new Error(data.error);
						}

						showToast(
							"Success",
							"Settings saved successfully",
							"success"
						);

						// Clear password fields
						document.getElementById("currentPassword").value = "";
						document.getElementById("newPassword").value = "";
						document.getElementById("confirmPassword").value = "";
					} catch (error) {
						console.error("Error saving settings:", error);
						showToast(
							"Error",
							"Error saving settings: " + error.message,
							"error"
						);
					} finally {
						// Reset button state
						submitButton.classList.remove("loading");
						submitButton.disabled = false;
						submitButton.innerHTML = originalText;
					}
				});

			function logout() {
				// Clear client-side cookie
				document.cookie =
					"adminToken=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;";

				// Redirect to logout endpoint to clear server-side session
				window.location.href = "/admin/logout";
			}
		</script>
	</body>
</html>
