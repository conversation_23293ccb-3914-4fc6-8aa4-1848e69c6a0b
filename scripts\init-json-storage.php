<?php

/**
 * Initialize JSON-based data storage
 * Creates default admin user when SQLite is not available
 */

declare(strict_types=1);

require_once __DIR__ . '/../config.php';

use DrxDion\Domain\Contracts\AdminRepositoryInterface;

try {
    echo "🔧 Initializing DrxDion JSON data storage...\n";

    $adminRepo = $container->get(AdminRepositoryInterface::class);

    // Check if we already have admin users
    if (!$adminRepo->hasAnyAdmins()) {
        echo "📝 Creating default admin user...\n";

        // Create default admin
        $defaultUsername = 'admin';
        $defaultPassword = 'drxdion2024';
        $defaultEmail = '<EMAIL>';

        $admin = $adminRepo->createDefaultAdmin($defaultUsername, $defaultPassword, $defaultEmail);

        echo "✅ Default admin user created successfully!\n";
        echo "   Username: {$defaultUsername}\n";
        echo "   Password: {$defaultPassword}\n";
        echo "   Email: {$defaultEmail}\n";
        echo "\n";
        echo "⚠️  Please change the default password after first login!\n";
    } else {
        echo "✅ Admin users already exist.\n";
    }

    // Create data directory if it doesn't exist
    $dataDir = __DIR__ . '/../data';
    if (!is_dir($dataDir)) {
        mkdir($dataDir, 0755, true);
        echo "📁 Created data directory: {$dataDir}\n";
    }

    echo "\n🚀 DrxDion JSON data storage initialization completed successfully!\n";
    echo "\nNOTE: This system is using JSON file storage as SQLite is not available.\n";
    echo "For production use, please enable SQLite3 extension in PHP.\n";
} catch (Throwable $e) {
    echo "❌ JSON data storage initialization failed: " . $e->getMessage() . "\n";
    echo "Error in: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    exit(1);
}
