# 🛍️ DrxDion E-commerce Platform

Modern, güvenli ve performanslı e-ticaret platformu. PHP 8.0+ Clean Architecture ile geliştirilmiştir.

## 🚀 Özellikler

### 💰 E-commerce Functionality
- ✅ Ürün satış sistemi
- ✅ İyzico ödeme entegrasyonu  
- ✅ Otomatik email bildirimleri
- ✅ Sipariş takip sistemi
- ✅ Müşteri bilgi yönetimi

### 🎛️ Admin Panel
- ✅ Modern, responsive tasarım
- ✅ Sipariş yönetimi ve filtreleme
- ✅ Storage dashboard (MySQL + JSON)
- ✅ Gerçek zamanlı sistem durumu
- ✅ Manuel/otomatik sync işlemleri
- ✅ Ayarlar paneli

### 🔒 Güvenlik
- ✅ CSRF koruması
- ✅ Rate limiting
- ✅ Input validation
- ✅ Secure headers
- ✅ Admin authentication
- ✅ Hassas dosya koruması

### ⚡ Performance
- ✅ Dual storage (MySQL + JSON fallback)
- ✅ Browser caching
- ✅ Gzip compression
- ✅ Minified assets
- ✅ Optimized database queries

## 🏗️ Sistem Gereksinimleri

- **PHP:** 8.0 veya üzeri
- **MySQL:** 5.7+ / 8.0+
- **Apache:** mod_rewrite etkin
- **SSL:** Gerekli (production için)
- **Composer:** Bağımlılık yönetimi için

## 📦 Kurulum

### 1. Dosyaları Sunucuya Yükleyin
```bash
# FTP/SFTP ile tüm dosyaları yükleyin
# VEYA
rsync -av --exclude='.git' ./ user@server:/path/to/domain/
```

### 2. Environment Ayarları
```bash
# Production environment dosyasını kopyalayın
cp .env.production .env

# Ayarları güncelleyin
nano .env
```

#### Önemli Environment Variables:
```env
DEBUG=false
BASE_URL=https://yourdomain.com
DB_HOST=localhost
DB_DATABASE=your_production_db
DB_USERNAME=your_db_user
DB_PASSWORD=your_secure_password
IYZIPAY_API_KEY=your_production_api_key
IYZIPAY_SECRET_KEY=your_production_secret_key
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

### 3. Veritabanı Kurulumu
```bash
# MySQL veritabanı ve tabloları oluşturun
php create-db.php

# Başarıyla tamamlandıktan sonra dosyayı silin
rm create-db.php
```

### 4. Admin Kullanıcısı Oluşturma
```bash
# Admin kullanıcısı oluşturun
php setup-admin.php

# Başarıyla tamamlandıktan sonra dosyayı silin
rm setup-admin.php
```

### 5. Dosya İzinleri
```bash
chmod 755 public/
chmod 755 public/uploads/
chmod 644 .htaccess
chmod 600 .env
chmod -R 755 src/
```

### 6. Güvenlik Kontrolü
```bash
# Kurulum sonrası güvenlik kontrolü
php tools/security-check.php
```

## 🔧 Bakım Araçları

### tools/ Klasörü
Sistem bakım araçları `tools/` klasöründe bulunur:

#### security-check.php
- **Amaç:** Production güvenlik kontrolü
- **Kullanım:** `php tools/security-check.php`
- **Sıklık:** Aylık kontrol önerilir
- **Çıktı:** Detaylı güvenlik raporu

#### performance-optimize.php
- **Amaç:** Performance optimizasyonu
- **Kullanım:** `php tools/performance-optimize.php`
- **Sıklık:** Kod güncellemeleri sonrası
- **Etki:** CSS/JS minification, caching, optimization

#### migrate-to-mysql.php
- **Amaç:** SQLite'dan MySQL'e veri migration
- **Kullanım:** `php tools/migrate-to-mysql.php`
- **Not:** Sadece migration gerektiğinde

## 🗄️ Veritabanı Yapısı

### orders Tablosu
```sql
CREATE TABLE orders (
    id VARCHAR(100) PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20),
    product_name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    shipping_cost DECIMAL(10,2) DEFAULT 0.00,
    payment_method VARCHAR(50) NOT NULL,
    payment_status VARCHAR(50) DEFAULT 'pending',
    payment_id VARCHAR(255) NULL,
    shipping_address TEXT NOT NULL,
    items TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_payment_status (payment_status),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_customer_email (customer_email)
);
```

### admin_users Tablosu  
```sql
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🎯 Admin Panel

### Erişim
- **URL:** https://yourdomain.com/admin
- **Login:** Kurulum sırasında oluşturulan admin bilgileri

### Özellikler
1. **Orders Management:** Sipariş listesi, filtreleme, durum güncelleme
2. **Storage Dashboard:** MySQL + JSON storage durumu, sync işlemleri
3. **Settings:** Admin profil ve sistem ayarları

### Storage Management
- **Dual Storage:** MySQL (primary) + JSON (fallback)
- **Automatic Fallback:** MySQL erişilemediğinde JSON kullanılır
- **Manual Sync:** Admin panelden manuel senkronizasyon
- **Sync Types:**
  - **Additive:** MySQL → JSON (ekleme/güncelleme)
  - **Replace:** JSON'u MySQL ile tamamen değiştir
  - **Force Replace:** Ekstra JSON siparişlerini sil

## 📧 Email Konfigürasyonu

### SMTP Ayarları
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_specific_password
ORDER_NOTIFICATION_TO=<EMAIL>
ORDER_NOTIFICATION_FROM=<EMAIL>
```

### App-Specific Password (Gmail)
1. Gmail hesabında 2FA aktif olmalı
2. Google Account → Security → App passwords
3. Yeni app password oluştur
4. Bu password'u EMAIL_PASS olarak kullan

## 💳 Ödeme Entegrasyonu (İyzico)

### Konfigürasyon
```env
IYZIPAY_API_KEY=your_production_api_key
IYZIPAY_SECRET_KEY=your_production_secret_key
IYZIPAY_URI=https://api.iyzipay.com
IYZICO_WEBHOOK_URL=https://yourdomain.com/api/iyzico-webhook
```

### Test Mode → Production
1. İyzico dashboard'dan production keys alın
2. Sandbox URL'leri production URL'leri ile değiştirin
3. Webhook URL'ini güncelleyin
4. Test ödemeleri yapın

## 🔄 Bakım İşlemleri

### Günlük
- [ ] Error log kontrolü
- [ ] Sipariş işlemleri kontrolü
- [ ] Payment webhook durumu

### Haftalık
- [ ] Database backup
- [ ] Log dosyaları temizliği
- [ ] Storage consistency kontrolü

### Aylık
- [ ] `php tools/security-check.php` çalıştır
- [ ] Dependencies güncelleme
- [ ] Performance review
- [ ] Backup verification

## 🆘 Sorun Giderme

### 500 Internal Server Error
```bash
# Apache error loglarını kontrol edin
tail -f /var/log/apache2/error.log

# PHP syntax kontrol
php -l index.php

# File permissions kontrol
ls -la .htaccess index.php
```

### Database Connection Hatası
```bash
# Database connection test
php -r "
try {
    \$pdo = new PDO('mysql:host=HOST;dbname=DB', 'USER', 'PASS');
    echo 'Database connection successful';
} catch (Exception \$e) {
    echo 'Connection failed: ' . \$e->getMessage();
}
"
```

### Payment İşlem Hataları
- API keys production değerlerini kontrol edin
- Webhook URL erişilebilir olmalı
- SSL sertifikası geçerli olmalı
- İyzico dashboard'dan transaction loglarını inceleyin

### Admin Panel Erişim Sorunu
```bash
# Admin şifre sıfırlama (acil durum)
php -r "
\$hash = password_hash('newpassword123', PASSWORD_DEFAULT);
echo 'New hash: ' . \$hash;
"
# Bu hash'i database'de admin_users tablosunda güncelle
```

## 📊 Monitoring

### Log Dosyaları
```bash
# Apache logs
tail -f /var/log/apache2/access.log
tail -f /var/log/apache2/error.log

# PHP error logs  
tail -f /var/log/php/error.log

# Application logs (eğer varsa)
tail -f data/app.log
```

### Performance Metrikleri
- Server response time
- Database query performance
- File upload sizes
- Storage usage
- Email delivery rates

## 🔐 Güvenlik Best Practices

### Sunucu Seviyesi
- [ ] SSL sertifikası aktif
- [ ] Firewall yapılandırılmış
- [ ] SSH key-based authentication
- [ ] Regular security updates
- [ ] Backup strategy

### Uygulama Seviyesi
- [ ] Strong admin passwords
- [ ] Environment variables güvenli
- [ ] File permissions correct
- [ ] Error reporting disabled (DEBUG=false)
- [ ] Sensitive endpoints protected

## 📞 Destek

### Teknik Destek İçin:
1. Server error loglarını kontrol edin
2. Configuration dosyalarını verify edin
3. Bu README'deki troubleshooting bölümünü inceleyin
4. tools/security-check.php çalıştırın

### Deployment Kontrol Listesi
- [ ] .env production değerleri güncellendi
- [ ] SSL sertifikası kuruldu
- [ ] Database oluşturuldu ve tablolar kuruldu
- [ ] Admin kullanıcısı oluşturuldu
- [ ] File permissions ayarlandı
- [ ] Güvenlik kontrolü yapıldı
- [ ] Test siparişi oluşturuldu
- [ ] Email bildirimleri test edildi
- [ ] Payment flow test edildi
- [ ] Admin panel erişimi test edildi

## 🎯 Başarı Kriterleri

Deployment başarılı sayılır:
- [x] Ana website yükleniyor
- [x] Admin panel erişilebilir
- [x] Sipariş oluşturulabiliyor
- [x] Ödemeler işleniyor
- [x] Email'ler gönderiliyor
- [x] Storage dashboard çalışıyor
- [x] Tüm güvenlik kontrolleri geçiyor
- [x] SSL sertifikası aktif

---

**Version:** 1.3.0  
**Last Updated:** July 2025  
**License:** Proprietary  
**Contact:** DrxDion Development Team
