<?php

/**
 * Production Security & Health Check
 * Bu dosyayı production'da çalıştırarak sistemi kontrol edin
 */

declare(strict_types=1);

echo "=== DRXDION PRODUCTION SECURITY CHECK ===\n\n";

// 1. Environment dosyası kontrolü
echo "1. Environment Configuration:\n";
if (file_exists('.env')) {
    echo "   ✅ .env file exists\n";

    // Hassas bilgileri kontrol et
    $envContent = file_get_contents('.env');
    $warnings = [];

    if (strpos($envContent, 'DEBUG=true') !== false) {
        $warnings[] = "   ⚠️  DEBUG mode is enabled (should be false in production)";
    }

    if (strpos($envContent, 'sandbox-') !== false) {
        $warnings[] = "   ⚠️  Sandbox API keys detected (should be production keys)";
    }

    if (strpos($envContent, 'localhost') !== false) {
        $warnings[] = "   ⚠️  Localhost URLs detected (should be production URLs)";
    }

    if (empty($warnings)) {
        echo "   ✅ Environment configuration looks good\n";
    } else {
        foreach ($warnings as $warning) {
            echo "$warning\n";
        }
    }
} else {
    echo "   ❌ .env file missing\n";
}

// 2. Kritik dosyaların varlığı
echo "\n2. Critical Files:\n";
$criticalFiles = [
    'index.php' => 'Main entry point',
    'router.php' => 'Router configuration',
    '.htaccess' => 'Apache configuration',
    'src/Infrastructure/Web/Routes/routes.php' => 'Route definitions',
    'public/index.html' => 'Main frontend page'
];

foreach ($criticalFiles as $file => $desc) {
    if (file_exists($file)) {
        echo "   ✅ $desc ($file)\n";
    } else {
        echo "   ❌ Missing: $desc ($file)\n";
    }
}

// 3. Güvenlik dosyaları
echo "\n3. Security Configuration:\n";
$htaccessContent = file_exists('.htaccess') ? file_get_contents('.htaccess') : '';

if (strpos($htaccessContent, 'X-Content-Type-Options') !== false) {
    echo "   ✅ Security headers configured\n";
} else {
    echo "   ⚠️  Security headers missing\n";
}

if (strpos($htaccessContent, 'Files ".env"') !== false) {
    echo "   ✅ .env file protection enabled\n";
} else {
    echo "   ❌ .env file not protected\n";
}

// 4. Klasör izinleri
echo "\n4. Directory Permissions:\n";
$directories = ['data', 'public/uploads'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "   ✅ $dir is writable\n";
        } else {
            echo "   ⚠️  $dir is not writable\n";
        }
    } else {
        echo "   ⚠️  $dir directory missing\n";
    }
}

// 5. Gereksiz dosya kontrolü
echo "\n5. Cleanup Check:\n";
$cleanupItems = [
    'test-*.php' => 'Test files',
    'debug-*.php' => 'Debug files',
    '*.log' => 'Log files',
    'cookies*.txt' => 'Cookie files'
];

$foundItems = [];
foreach ($cleanupItems as $pattern => $desc) {
    $files = glob($pattern);
    if (!empty($files)) {
        $foundItems[] = "$desc: " . implode(', ', $files);
    }
}

if (empty($foundItems)) {
    echo "   ✅ No unnecessary files found\n";
} else {
    echo "   ⚠️  Found files that should be removed:\n";
    foreach ($foundItems as $item) {
        echo "      - $item\n";
    }
}

// 6. Database bağlantı testi
echo "\n6. Database Connection:\n";
try {
    require_once 'config.php';
    if (defined('DB_HOST')) {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USER,
            DB_PASS,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        echo "   ✅ Database connection successful\n";

        // Tabloları kontrol et
        $tables = ['orders', 'admin_users'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "   ✅ Table '$table' exists\n";
            } else {
                echo "   ⚠️  Table '$table' missing\n";
            }
        }
    } else {
        echo "   ❌ Database configuration missing\n";
    }
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
}

echo "\n=== SECURITY CHECK COMPLETE ===\n";
echo "\nRECOMMENDATIONS:\n";
echo "1. Review all ⚠️  and ❌ items above\n";
echo "2. Update .env with production values\n";
echo "3. Test all functionality manually\n";
echo "4. Enable SSL certificate\n";
echo "5. Set up regular backups\n";
echo "6. Monitor server logs\n";
echo "7. Remove this security check file after verification\n";
