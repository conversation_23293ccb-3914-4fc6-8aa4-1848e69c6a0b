<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Processing Payment - DrxDion</title>
		<link href="/assets/css/bootstrap.min.css" rel="stylesheet" />
	</head>
	<body class="bg-light">
		<div class="container">
			<div class="text-center" style="margin-top: 100px">
				<div class="spinner-border text-primary" role="status">
					<span class="visually-hidden">Loading...</span>
				</div>
				<h4 class="mt-3">Processing your payment...</h4>
				<p class="text-muted">Please don't close this window.</p>
			</div>
		</div>
		<script src="/assets/js/bootstrap.bundle.min.js"></script>
		<script>
			document.addEventListener("DOMContentLoaded", function () {
				// Get URL parameters
				const urlParams = new URLSearchParams(window.location.search);
				const status = urlParams.get("status");
				const message = urlParams.get("message");
				const orderId = urlParams.get("orderId");

				const container = document.querySelector(".container");

				if (status === "success") {
					container.innerHTML = `
						<div class="text-center my-5">
							<div class="bg-white p-5 rounded shadow">
								<div class="text-success mb-4">
									<i class="bi bi-check-circle" style="font-size: 4rem;"></i>
								</div>
								<h1>Payment Successful!</h1>
								<p class="lead">Your order has been placed successfully.</p>
								<p class="mb-4">Order ID: ${orderId}</p>
								<p>We'll send you a confirmation email shortly.</p>
								<a href="/" class="btn btn-primary mt-3">Return to Homepage</a>
							</div>
						</div>
					`;
				} else if (status === "error") {
					container.innerHTML = `
						<div class="text-center my-5">
							<div class="bg-white p-5 rounded shadow">
								<div class="text-danger mb-4">
									<i class="bi bi-exclamation-circle" style="font-size: 4rem;"></i>
								</div>
								<h1>Payment Failed</h1>
								<p class="lead">${message || "There was an issue with your payment."}</p>
								<p>Please try again or choose a different payment method.</p>
								<a href="/" class="btn btn-primary mt-3">Return to Homepage</a>
							</div>
						</div>
					`;
				}
			});
		</script>
	</body>
</html>
