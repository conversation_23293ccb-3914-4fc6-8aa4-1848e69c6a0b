# DrxDion E-Commerce - İyzico Webhook Entegrasyonu

## 🔗 **Webhook Entegrasyonu**

Bu proje İyzico link'leri (`https://sandbox.iyzi.link/ZGM`) üzerinden yapılan ödemeleri webhook ile otomatik olarak işler ve yeni siparişler oluşturur.

### ✅ **Nasıl Çalışır:**

1. **Müşteri İyzico Link'ine Gider**: Kredi kartı ödeme seçildiğinde müşteri `data-payment-link` üzerinden İyzico'ya yönlendirilir
2. **Ödeme Yapılır**: Müşteri İyzico üzerinde ödemeyi tamamlar
3. **Webhook Çağrılır**: İyzico başarılı ödeme sonrası webhook'umuzu çağırır
4. **Yeni Sipariş Oluşturulur**: Webhook ile gelen bilgilerden otomatik olarak yeni sipariş oluşturulur
5. **Email Bildirimleri**: Hem admin hem müşteriye email gönderilir

### 🔧 **Kurulum:**

#### 1. Environment Variables (.env)
```env
# İyzico Webhook Configuration
IYZICO_WEBHOOK_SECRET=your_webhook_secret_here
IYZICO_WEBHOOK_URL=https://yourdomain.com/api/iyzico-webhook
```

#### 2. İyzico Merchant Panel Ayarları:
- **Webhook URL**: `https://yourdomain.com/api/iyzico-webhook`
- **Events**: Payment Success, Payment Failed
- **Method**: POST
- **Content Type**: application/json

#### 3. SSL Sertifikası:
İyzico webhook'ları sadece HTTPS URL'lere çalışır. Production'da SSL sertifikası gereklidir.

### 📡 **Webhook Endpoint:**

**URL**: `POST /api/iyzico-webhook`

**Örnek Payload:**
```json
{
    "status": "success",
    "paymentStatus": "SUCCESS",
    "paymentId": "payment-123",
    "paymentTransactionId": "tx-456",
    "itemName": "DrxDion Product",
    "price": "99.00",
    "paidPrice": "109.00",
    "buyerName": "Customer Name",
    "buyerEmail": "<EMAIL>",
    "shippingAddress": "Customer Address"
}
```

### 🧪 **Test Etme:**

#### Local Test:
```bash
# Sunucuyu başlat
npm start

# Test webhook'u çalıştır
node test-webhook.js
```

#### Production Test:
1. İyzico sandbox link'ini kullanarak test ödemesi yapın
2. Server log'larını kontrol edin
3. Admin panel'den siparişlerin oluştuğunu kontrol edin

### 📊 **Monitoring:**

Webhook çağrıları için log'lar:
- Gelen webhook payload'ları
- Signature validation
- Sipariş oluşturma durumu
- Email gönderme durumu

### 🔒 **Güvenlik:**

- ✅ CSRF protection'dan muaf (webhook için gerekli)
- ✅ Signature validation (opsiyonel)
- ✅ JSON payload validation
- ✅ Error handling

### 📧 **Email Bildirimleri:**

**Admin Email:**
- Yeni sipariş bilgisi
- Ödeme detayları
- Müşteri bilgileri

**Müşteri Email:**
- Sipariş onayı
- Ödeme başarılı bildirimi

### 🚀 **Production Deployment:**

1. SSL sertifikası kur
2. Environment variables'ları production değerleriyle güncelle
3. İyzico merchant panel'de production webhook URL'i ayarla
4. Webhook secret'ı güvenli bir şekilde sayla
5. Log monitoring sistemi kur

---

## 📝 **Troubleshooting:**

### Webhook Çalışmıyor:
- SSL sertifikası kontrol et
- İyzico panel ayarlarını kontrol et
- Server log'larını kontrol et
- Firewall ayarlarını kontrol et

### Email Gönderilmiyor:
- SMTP ayarlarını kontrol et
- Email credentials'ları kontrol et
- Spam klasörünü kontrol et

### Sipariş Oluşturulmuyor:
- Database connection'ı kontrol et
- Webhook payload formatını kontrol et
- Server error log'larını kontrol et
