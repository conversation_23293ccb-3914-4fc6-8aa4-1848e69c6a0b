{"SMART_TEST_20250703225052_f7d9ac36": {"id": "SMART_TEST_20250703225052_f7d9ac36", "product_name": "Smart Test Product", "price": 89.99, "total_price": 109.99, "shipping_cost": 20, "payment_method": "credit_card", "payment_status": "pending", "payment_id": null, "customer_name": "Smart Test Customer", "customer_email": "<EMAIL>", "shipping_address": "789 Smart Street, Smart City, SC 78901", "created_at": "2025-07-03 22:50:52", "updated_at": "2025-07-03 22:50:52"}, "ORDER_20250703224222_473bbbc4": {"id": "ORDER_20250703224222_473bbbc4", "product_name": "API Test Product", "price": 99.99, "total_price": 119.99, "shipping_cost": 0, "payment_method": "cod", "payment_status": "pending", "payment_id": null, "customer_name": "API Test Customer", "customer_email": "<EMAIL>", "shipping_address": "456 API Street, API City, API 67890", "created_at": "2025-07-03 22:42:22", "updated_at": "2025-07-03 22:42:22"}, "ORDER_20250703224154_91a8ebd5": {"id": "ORDER_20250703224154_91a8ebd5", "product_name": "API Test Product", "price": 99.99, "total_price": 119.99, "shipping_cost": 0, "payment_method": "cod", "payment_status": "pending", "payment_id": null, "customer_name": "API Test Customer", "customer_email": "<EMAIL>", "shipping_address": "456 API Street, API City, API 67890", "created_at": "2025-07-03 22:41:54", "updated_at": "2025-07-03 22:41:54"}, "COMPOSITE_TEST_20250703223939_e530a7cf": {"id": "COMPOSITE_TEST_20250703223939_e530a7cf", "product_name": "Composite Test Product", "price": 129.99, "total_price": 149.99, "shipping_cost": 20, "payment_method": "credit_card", "payment_status": "paid", "payment_id": null, "customer_name": "Test Customer", "customer_email": "<EMAIL>", "shipping_address": "123 Test Street, Test City, TC 12345", "created_at": "2025-07-03 22:39:39", "updated_at": "2025-07-03 22:39:39"}, "ORDER_20250703151551_3c5e5286": {"id": "ORDER_20250703151551_3c5e5286", "product_name": "MediPulse Therapy System", "price": 899, "total_price": 919, "shipping_cost": 20, "payment_method": "card", "payment_status": "pending", "payment_id": null, "customer_name": "Anonymous", "customer_email": "<EMAIL>", "shipping_address": "<PERSON><PERSON><PERSON>", "created_at": "2025-07-03 15:15:51", "updated_at": "2025-07-03 15:15:51"}, "TEST123": {"id": "TEST123", "product_name": "Test Product", "price": 100, "total_price": 110, "shipping_cost": 10, "payment_method": "cod", "payment_status": "pending", "payment_id": null, "customer_name": "Test User", "customer_email": "<EMAIL>", "shipping_address": "123 Test St", "created_at": "2025-07-03 15:12:05", "updated_at": "2025-07-03 15:12:05"}, "ORDER_20250703144851_70479e84": {"id": "ORDER_20250703144851_70479e84", "product_name": "MediPulse Therapy System", "price": 899, "total_price": 899, "shipping_cost": 0, "payment_method": "", "payment_status": "pending", "payment_id": null, "customer_name": "Anonymous", "customer_email": "<EMAIL>", "shipping_address": "asdasdasd", "created_at": "2025-07-03 14:48:51", "updated_at": "2025-07-03 14:48:51"}, "ORDER_20250703144624_94c71099": {"id": "ORDER_20250703144624_94c71099", "product_name": "MediPulse Therapy System", "price": 899, "total_price": 919, "shipping_cost": 20, "payment_method": "", "payment_status": "pending", "payment_id": null, "customer_name": "Anonymous", "customer_email": "<EMAIL>", "shipping_address": "asdasdasd", "created_at": "2025-07-03 14:46:24", "updated_at": "2025-07-03 14:46:24"}, "ORDER_20250703144608_bfe37c8f": {"id": "ORDER_20250703144608_bfe37c8f", "product_name": "MediPulse Therapy System", "price": 899, "total_price": 899, "shipping_cost": 0, "payment_method": "", "payment_status": "pending", "payment_id": null, "customer_name": "Anonymous", "customer_email": "<EMAIL>", "shipping_address": "asdasdasd", "created_at": "2025-07-03 14:46:08", "updated_at": "2025-07-03 14:46:08"}, "ORDER_20250703110534_05ee0306": {"id": "ORDER_20250703110534_05ee0306", "product_name": "MediPulse Therapy System", "price": 899, "total_price": 899, "shipping_cost": 0, "payment_method": "", "payment_status": "pending", "payment_id": null, "customer_name": "Anonymous", "customer_email": "<EMAIL>", "shipping_address": "asdasdasd asd asd asd", "created_at": "2025-07-03 11:05:34", "updated_at": "2025-07-03 11:05:34"}, "ORDER_20250703105815_8cc769fb": {"id": "ORDER_20250703105815_8cc769fb", "product_name": "MediPulse Therapy System", "price": 899, "total_price": 899, "shipping_cost": 0, "payment_method": "", "payment_status": "pending", "payment_id": null, "customer_name": "Anonymous", "customer_email": "<EMAIL>", "shipping_address": "<PERSON><PERSON><PERSON>das<PERSON>", "created_at": "2025-07-03 10:58:15", "updated_at": "2025-07-03 10:58:15"}, "ORDER_20250703105748_b8312701": {"id": "ORDER_20250703105748_b8312701", "product_name": "Test Product", "price": 50, "total_price": 60, "shipping_cost": 0, "payment_method": "", "payment_status": "pending", "payment_id": null, "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "shipping_address": "123 Test St", "created_at": "2025-07-03 10:57:48", "updated_at": "2025-07-03 10:57:48"}, "TEST_1750960887_4": {"id": "TEST_1750960887_4", "product_name": "Product 4", "price": 50, "total_price": 100, "shipping_cost": 0, "payment_method": "", "payment_status": "cancelled", "payment_id": null, "customer_name": "Customer 4", "customer_email": "<EMAIL>", "shipping_address": "123 Test Street, Test City, TC 12345", "created_at": "2025-06-26 21:01:27", "updated_at": "2025-06-26 21:01:27"}, "TEST_1750960886_3": {"id": "TEST_1750960886_3", "product_name": "Product 3", "price": 50, "total_price": 75, "shipping_cost": 0, "payment_method": "", "payment_status": "completed", "payment_id": null, "customer_name": "Customer 3", "customer_email": "<EMAIL>", "shipping_address": "123 Test Street, Test City, TC 12345", "created_at": "2025-06-26 21:01:26", "updated_at": "2025-06-26 21:01:26"}, "TEST_1750960885": {"id": "TEST_1750960885", "product_name": "Test Product", "price": 50, "total_price": 100, "shipping_cost": 0, "payment_method": "", "payment_status": "completed", "payment_id": "PAY_1751480305", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "shipping_address": "123 Test Street, Test City, TC 12345", "created_at": "2025-06-26 21:01:25", "updated_at": "2025-07-03 09:04:52"}, "TEST_1750960885_2": {"id": "TEST_1750960885_2", "product_name": "Product 2", "price": 50, "total_price": 50, "shipping_cost": 0, "payment_method": "", "payment_status": "failed", "payment_id": "PAY_FAILED_1751480346", "customer_name": "Customer 2", "customer_email": "<EMAIL>", "shipping_address": "123 Test Street, Test City, TC 12345", "created_at": "2025-06-26 21:01:25", "updated_at": "2025-07-02 21:19:06"}}