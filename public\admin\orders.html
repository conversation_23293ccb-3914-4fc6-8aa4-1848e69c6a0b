<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Admin Orders - DrxDion</title>
		<link href="/assets/css/bootstrap.min.css" rel="stylesheet" />
		<link href="/assets/css/bootstrap-icons.css" rel="stylesheet" />
		<style>:root{--primary-color:#6366f1;--secondary-color:#f8fafc;--success-color:#10b981;--warning-color:#f59e0b;--danger-color:#ef4444;--gray-50:#f9fafb;--gray-100:#f3f4f6;--gray-200:#e5e7eb;--gray-300:#d1d5db;--gray-600:#4b5563;--gray-800:#1f2937;--shadow-sm:0 1px 2px 0 rgba(0,0,0,0.05);--shadow-md:0 4px 6px -1px rgba(0,0,0,0.1),0 2px 4px -1px rgba(0,0,0,0.06);--shadow-lg:0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05);}body{background:linear-gradient(135deg,var(--gray-50) 0%,var(--gray-100) 100%);font-family:"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;min-height:100vh;}.navbar{background:linear-gradient(135deg,var(--primary-color) 0%,#4f46e5 100%) !important;box-shadow:var(--shadow-lg);border:none;}.navbar-brand{font-weight:700;font-size:1.5rem;color:white !important;}.orders-container{padding:2.5rem 1rem;max-width:1400px;}.orders-header{background:linear-gradient(135deg,white 0%,var(--gray-50) 100%);border-radius:20px;padding:2.5rem;margin-bottom:2rem;box-shadow:var(--shadow-lg);border:1px solid var(--gray-200);position:relative;overflow:hidden;}.orders-header::before{content:"";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,var(--primary-color),#4f46e5,var(--success-color));}.orders-header h2{font-weight:800;color:var(--gray-800);margin:0 0 1.5rem 0;display:flex;align-items:center;gap:0.75rem;font-size:2rem;}.orders-header h2 i{background:linear-gradient(135deg,var(--primary-color),#4f46e5);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;}.filters-section{background:linear-gradient(135deg,white 0%,var(--gray-50) 100%);border-radius:16px;padding:1.75rem;margin-top:1.5rem;box-shadow:var(--shadow-md);border:1px solid var(--gray-200);position:relative;overflow:hidden;}.filters-section::before{content:"";position:absolute;top:0;left:0;right:0;height:2px;background:linear-gradient(90deg,var(--primary-color),#4f46e5,var(--success-color));}.filters-row{display:flex;gap:1.25rem;align-items:end;flex-wrap:wrap;}.filter-item{display:flex;flex-direction:column;gap:0.5rem;}.filter-item label{font-size:0.75rem;font-weight:700;color:var(--gray-600);text-transform:uppercase;letter-spacing:0.05em;margin-bottom:0;}.filter-item.search{flex:1;min-width:300px;}.filter-item.select{min-width:150px;}.search-container{position:relative;}#searchInput{border:2px solid var(--gray-200);border-radius:12px;padding:0.875rem 1rem 0.875rem 3rem;font-size:0.9rem;transition:all 0.3s ease;background:white;width:100%;font-weight:500;}#searchInput:focus{border-color:var(--primary-color);box-shadow:0 0 0 4px rgba(99,102,241,0.12);outline:none;transform:translateY(-1px);}.search-icon{position:absolute;left:1.125rem;top:50%;transform:translateY(-50%);color:var(--gray-500);z-index:2;font-size:1rem;}.form-select,.form-control{border:2px solid var(--gray-200);border-radius:12px;padding:0.875rem 1rem;font-size:0.9rem;transition:all 0.3s ease;background:white;font-weight:500;}.form-select:focus,.form-control:focus{border-color:var(--primary-color);box-shadow:0 0 0 4px rgba(99,102,241,0.12);outline:none;transform:translateY(-1px);}.form-select:hover,.form-control:hover{border-color:var(--gray-300);transform:translateY(-1px);}.date-range{display:flex;align-items:center;gap:0.75rem;background:var(--gray-50);padding:0.75rem 1rem;border-radius:12px;border:2px solid var(--gray-200);transition:all 0.3s ease;}.date-range:hover{border-color:var(--primary-color);box-shadow:0 0 0 2px rgba(99,102,241,0.08);}.date-range input{width:140px;border:1px solid var(--gray-300);border-radius:8px;padding:0.5rem 0.75rem;font-size:0.85rem;background:white;font-weight:500;}.date-range input:focus{border-color:var(--primary-color);box-shadow:0 0 0 2px rgba(99,102,241,0.08);outline:none;}.date-separator{color:var(--gray-600);font-size:0.8rem;font-weight:600;padding:0 0.25rem;}#applyDateFilter{background:linear-gradient(135deg,var(--primary-color) 0%,#4f46e5 100%);border:none;border-radius:10px;padding:0.875rem 1.5rem;color:white;font-size:0.9rem;font-weight:700;transition:all 0.3s ease;display:flex;align-items:center;gap:0.5rem;box-shadow:var(--shadow-sm);text-transform:uppercase;letter-spacing:0.025em;}#applyDateFilter:hover{transform:translateY(-2px);box-shadow:var(--shadow-lg);background:linear-gradient(135deg,#4f46e5 0%,#4338ca 100%);}#applyDateFilter:active{transform:translateY(0);}#applyDateFilter.loading{background:var(--gray-400);cursor:not-allowed;transform:none;}#applyDateFilter.loading:hover{background:var(--gray-400);transform:none;box-shadow:var(--shadow-sm);}.btn-clear-filters{background:linear-gradient(135deg,var(--gray-600) 0%,var(--gray-700) 100%);border:none;border-radius:10px;padding:0.875rem 1.25rem;color:white;font-size:0.9rem;font-weight:600;transition:all 0.3s ease;display:flex;align-items:center;gap:0.5rem;box-shadow:var(--shadow-sm);}.btn-clear-filters:hover{background:linear-gradient(135deg,var(--gray-700) 0%,var(--gray-800) 100%);transform:translateY(-2px);box-shadow:var(--shadow-md);}.btn-clear-filters:active{transform:translateY(0);}.order-card{background:white;border-radius:16px;margin-bottom:1.5rem;border:1px solid var(--gray-200);transition:all 0.3s ease;overflow:hidden;box-shadow:var(--shadow-sm);}.order-card:hover{transform:translateY(-2px);box-shadow:var(--shadow-md);border-color:var(--primary-color);}.order-card-header{background:var(--gray-50);padding:1rem 1.5rem;border-bottom:1px solid var(--gray-200);display:flex;justify-content:space-between;align-items:center;}.order-id{font-weight:600;color:var(--gray-800);font-size:1rem;}.order-date{font-size:0.875rem;color:var(--gray-600);}.order-card-body{padding:1.5rem;}.order-info{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1.5rem;margin-bottom:1.5rem;}.info-item{display:flex;flex-direction:column;gap:0.25rem;}.info-label{font-size:0.75rem;font-weight:600;color:var(--gray-600);text-transform:uppercase;letter-spacing:0.05em;}.info-value{font-size:0.95rem;color:var(--gray-800);font-weight:500;}.price-value{font-size:1.125rem;font-weight:700;color:var(--primary-color);}.status-badge{display:inline-flex;align-items:center;gap:0.5rem;padding:0.5rem 1rem;border-radius:50px;font-size:0.875rem;font-weight:600;text-transform:capitalize;}.status-pending{background:rgba(245,158,11,0.1);color:#f59e0b;border:1px solid rgba(245,158,11,0.2);}.status-paid{background:rgba(16,185,129,0.1);color:#10b981;border:1px solid rgba(16,185,129,0.2);}.status-completed{background:rgba(99,102,241,0.1);color:#6366f1;border:1px solid rgba(99,102,241,0.2);}.status-cancelled{background:rgba(239,68,68,0.1);color:#ef4444;border:1px solid rgba(239,68,68,0.2);}.status-failed{background:rgba(239,68,68,0.1);color:#ef4444;border:1px solid rgba(239,68,68,0.2);}.order-actions{display:flex;gap:1rem;align-items:center;justify-content:space-between;padding-top:1.5rem;border-top:1px solid var(--gray-200);}.status-select{border:2px solid var(--gray-200);border-radius:8px;padding:0.5rem 0.75rem;font-size:0.875rem;transition:all 0.2s ease;background:white;min-width:140px;}.status-select:focus{border-color:var(--primary-color);box-shadow:0 0 0 3px rgba(99,102,241,0.1);outline:none;}.delete-btn{background:linear-gradient(135deg,var(--danger-color) 0%,#dc2626 100%);border:none;border-radius:8px;padding:0.5rem 1rem;color:white;font-size:0.875rem;font-weight:600;transition:all 0.2s ease;display:flex;align-items:center;gap:0.5rem;}.delete-btn:hover{transform:translateY(-1px);box-shadow:var(--shadow-md);background:linear-gradient(135deg,#dc2626 0%,#b91c1c 100%);}.empty-state{text-align:center;padding:4rem 2rem;background:white;border-radius:16px;border:1px solid var(--gray-200);}.empty-state-icon{font-size:4rem;color:var(--gray-300);margin-bottom:1rem;}.empty-state-title{font-size:1.5rem;font-weight:700;color:var(--gray-800);margin-bottom:0.5rem;}.empty-state-text{color:var(--gray-600);font-size:1rem;}.pagination{margin-top:2rem;background:white;padding:1.5rem;border-radius:16px;box-shadow:var(--shadow-sm);border:1px solid var(--gray-200);justify-content:center;}.pagination .page-link{border:2px solid var(--gray-200);color:var(--gray-700);padding:0.75rem 1rem;margin:0 0.25rem;border-radius:10px;font-weight:600;transition:all 0.3s ease;background:white;}.pagination .page-link:hover{background:var(--primary-color);border-color:var(--primary-color);color:white;transform:translateY(-2px);box-shadow:var(--shadow-md);}.pagination .page-item.active .page-link{background:linear-gradient(135deg,var(--primary-color) 0%,#4f46e5 100%);border-color:var(--primary-color);color:white;box-shadow:var(--shadow-md);}.pagination .page-item.disabled .page-link{color:var(--gray-400);background:var(--gray-100);border-color:var(--gray-200);}.stats-bar{background:white;border-radius:16px;padding:1rem 1.5rem;margin-bottom:1.5rem;box-shadow:var(--shadow-sm);border:1px solid var(--gray-200);display:flex;justify-content:space-between;align-items:center;}.stats-text{color:var(--gray-600);font-size:0.9rem;font-weight:500;}.stats-count{color:var(--primary-color);font-weight:700;}@media (max-width:768px){.orders-header{padding:1.5rem;}.orders-header h2{font-size:1.5rem;margin-bottom:1rem;}.filters-section{padding:1.5rem;margin-top:1rem;}.filters-row{flex-direction:column;align-items:stretch;gap:1.5rem;}.filter-item.search{min-width:auto;}.filter-item.select{min-width:auto;}.date-range{flex-direction:column;gap:1rem;padding:1.25rem;}.date-range input{width:100%;}.date-separator{display:none;}#applyDateFilter{width:100%;justify-content:center;padding:1rem 1.5rem;}.btn-clear-filters{width:100%;justify-content:center;padding:1rem 1.5rem;}.order-info{grid-template-columns:1fr;gap:1rem;}.order-actions{flex-direction:column;gap:1rem;align-items:stretch;}.status-select{min-width:auto;width:100%;}.delete-btn{width:100%;justify-content:center;}}</style>
	</head>
	<body>
		<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
			<div class="container-fluid">
				<a class="navbar-brand" href="/admin/orders">DrxDion Admin</a>
				<button
					class="navbar-toggler"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#navbarNav"
				>
					<span class="navbar-toggler-icon"></span>
				</button>
				<div class="collapse navbar-collapse" id="navbarNav">
					<ul class="navbar-nav">
						<li class="nav-item">
							<a class="nav-link active" href="/admin/orders"
								>Orders</a
							>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="/admin/settings"
								>Settings</a
							>
						</li>
						<li class="nav-item">
							<a class="nav-link" href="/admin/storage"
								>Storage</a
							>
						</li>
					</ul>
					<ul class="navbar-nav ms-auto">
						<li class="nav-item">
							<button
								class="btn btn-outline-light"
								onclick="logout()"
							>
								Logout
							</button>
						</li>
					</ul>
				</div>
			</div>
		</nav>

		<div class="container orders-container">
			<div class="orders-header">
				<h2>
					<i class="bi bi-box-seam"></i>
					Orders Management
				</h2>
				<p class="text-muted mb-0">
					Manage and track all customer orders
				</p>

				<div class="filters-section">
					<div class="filters-row">
						<!-- Search -->
						<div class="filter-item search">
							<label>Search</label>
							<div class="search-container">
								<i class="bi bi-search search-icon"></i>
								<input
									type="text"
									class="form-control"
									id="searchInput"
									placeholder="Search by customer, email or order ID..."
								/>
							</div>
						</div>

						<!-- Payment Method Filter -->
						<div class="filter-item select">
							<label>Payment Method</label>
							<select
								class="form-select"
								id="paymentMethodFilter"
							>
								<option value="">All Methods</option>
								<option value="cod">Cash on Delivery</option>
								<option value="card">Credit Card</option>
							</select>
						</div>

						<!-- Status Filter -->
						<div class="filter-item select">
							<label>Status</label>
							<select class="form-select" id="statusFilter">
								<option value="">All Statuses</option>
								<option value="pending">Pending</option>
								<option value="paid">Paid</option>
								<option value="completed">Completed</option>
								<option value="cancelled">Cancelled</option>
								<option value="failed">Failed</option>
							</select>
						</div>

						<!-- Date Range Filter -->
						<div class="filter-item">
							<label>Date Range</label>
							<div class="date-range">
								<input
									type="date"
									class="form-control"
									id="dateFromFilter"
								/>
								<span class="date-separator">to</span>
								<input
									type="date"
									class="form-control"
									id="dateToFilter"
								/>
								<button id="applyDateFilter">
									<i class="bi bi-funnel"></i>
									Apply
								</button>
							</div>
						</div>

						<!-- Clear Filters Button -->
						<div class="filter-item">
							<label>&nbsp;</label>
							<button
								id="clearFilters"
								class="btn-clear-filters"
								style="display: none"
							>
								<i class="bi bi-x-circle"></i>
								Clear All
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Stats Bar -->
			<div class="stats-bar" id="statsBar" style="display: none">
				<div class="stats-text">
					Showing
					<span class="stats-count" id="currentResults">0</span>
					orders
					<span id="totalResults">
						of <span class="stats-count">0</span> total</span
					>
				</div>
				<div class="stats-text">
					Page
					<span class="stats-count" id="currentPageDisplay">1</span>
					of <span class="stats-count" id="totalPagesDisplay">1</span>
				</div>
			</div>

			<div id="ordersList"></div>

			<div
				class="d-flex justify-content-center"
				id="paginationContainer"
				style="display: none"
			>
				<nav aria-label="Orders pagination" class="pagination">
					<ul class="pagination mb-0" id="pagination"></ul>
				</nav>
			</div>
		</div>

		<!-- Toast Container -->
		<div class="toast-container position-fixed bottom-0 end-0 p-3">
			<div
				id="liveToast"
				class="toast"
				role="alert"
				aria-live="assertive"
				aria-atomic="true"
			>
				<div class="toast-header">
					<strong class="me-auto" id="toastTitle"
						>Notification</strong
					>
					<button
						type="button"
						class="btn-close"
						data-bs-dismiss="toast"
						aria-label="Close"
					></button>
				</div>
				<div class="toast-body" id="toastMessage">
					<!-- Message will be inserted here -->
				</div>
			</div>
		</div>

		<script src="/assets/js/bootstrap.bundle.min.js"></script>
		<script>
			let currentPage = 1;
			const limit = 10;

			// Keep track of currently active filters (separate from form values)
			let activeFilters = {
				search: "",
				paymentMethod: "",
				status: "",
				dateFrom: "",
				dateTo: "",
			};

			// Utility function to get CSRF token (first try cookie, then API)
			async function getCSRFToken() {
				// First try to get from cookie
				const cookieToken = getCookie("csrf-token");
				if (cookieToken) {
					return cookieToken;
				}

				// If not in cookie, get from API
				try {
					const response = await fetch("/api/csrf-token", {
						credentials: "include",
					});
					const data = await response.json();
					return data.token || "";
				} catch (error) {
					console.error("Error getting CSRF token:", error);
					return "";
				}
			}

			// Utility function to get cookie value
			function getCookie(name) {
				const value = `; ${document.cookie}`;
				const parts = value.split(`; ${name}=`);
				if (parts.length === 2) return parts.pop().split(";").shift();
				return "";
			}

			// Toast notification function
			function showToast(title, message, type = "success") {
				const toastElement = document.getElementById("liveToast");
				const toastTitle = document.getElementById("toastTitle");
				const toastMessage = document.getElementById("toastMessage");
				const toastHeader = toastElement.querySelector(".toast-header");

				// Set title and message
				toastTitle.textContent = title;
				toastMessage.textContent = message;

				// Remove existing color classes and add new one
				toastHeader.classList.remove(
					"text-bg-success",
					"text-bg-danger",
					"text-bg-warning",
					"text-bg-info"
				);

				switch (type) {
					case "success":
						toastHeader.classList.add("text-bg-success");
						break;
					case "error":
					case "danger":
						toastHeader.classList.add("text-bg-danger");
						break;
					case "warning":
						toastHeader.classList.add("text-bg-warning");
						break;
					case "info":
						toastHeader.classList.add("text-bg-info");
						break;
				}

				// Show toast
				const toast = new bootstrap.Toast(toastElement);
				toast.show();
			} // Helper function to format dates for API with correct timezone handling
			function formatDateForApi(dateStr, isEndDate = false) {
				if (!dateStr) return null;

				console.log("Input date string:", dateStr);

				// Parse the date string correctly (assuming format YYYY-MM-DD)
				const [year, month, day] = dateStr.split("-").map(Number);

				// Create date in local timezone
				const date = new Date(year, month - 1, day);
				console.log("Parsed local date:", date.toString());

				// Set hours based on start or end of day
				if (isEndDate) {
					date.setHours(23, 59, 59, 999);
				} else {
					date.setHours(0, 0, 0, 0);
				}

				// Create UTC date with the same components
				const utcDate = new Date(
					Date.UTC(
						date.getFullYear(),
						date.getMonth(),
						date.getDate(),
						date.getHours(),
						date.getMinutes(),
						date.getSeconds(),
						date.getMilliseconds()
					)
				);

				console.log("Final UTC date:", utcDate.toISOString());
				return utcDate.toISOString();
			}

			// Get current filters state
			function getCurrentFilters() {
				return {
					page: currentPage,
					search: activeFilters.search,
					paymentMethod: activeFilters.paymentMethod,
					status: activeFilters.status,
					dateFrom: activeFilters.dateFrom,
					dateTo: activeFilters.dateTo,
				};
			} // Check if any filters are active
			function hasActiveFilters() {
				return !!(
					activeFilters.search ||
					activeFilters.paymentMethod ||
					activeFilters.status ||
					activeFilters.dateFrom ||
					activeFilters.dateTo
				);
			}

			// Update clear filters button visibility
			function updateClearFiltersButton() {
				const clearButton = document.getElementById("clearFilters");
				if (hasActiveFilters()) {
					clearButton.style.display = "flex";
				} else {
					clearButton.style.display = "none";
				}
			}

			// Unified fetch orders function
			async function fetchOrders(filters = {}) {
				try {
					// Show loading state
					document.getElementById("ordersList").innerHTML = `
						<div class="d-flex justify-content-center py-5">
							<div class="spinner-border text-primary" role="status">
								<span class="visually-hidden">Loading...</span>
							</div>
						</div>
					`;

					// Build URL with query parameters
					const queryParams = new URLSearchParams();
					queryParams.append("page", filters.page || 1);
					queryParams.append("limit", limit);

					if (filters.search) {
						queryParams.append("search", filters.search);
					}
					if (filters.paymentMethod) {
						queryParams.append(
							"payment_method",
							filters.paymentMethod
						);
					}
					if (filters.status) {
						queryParams.append("status", filters.status);
					}

					// Handle date filters
					if (filters.dateFrom || filters.dateTo) {
						console.log(
							"Processing date filters:",
							filters.dateFrom,
							filters.dateTo
						);

						const dateFrom = formatDateForApi(filters.dateFrom);
						const dateTo = formatDateForApi(filters.dateTo, true);

						if (dateFrom) {
							queryParams.append("date_from", dateFrom);
							console.log("Added date_from:", dateFrom);
						}
						if (dateTo) {
							queryParams.append("date_to", dateTo);
							console.log("Added date_to:", dateTo);
						}
					}

					const finalUrl = `/api/orders?${queryParams.toString()}`;
					console.log("Final API URL:", finalUrl);

					const response = await fetch(
						`/api/orders?${queryParams.toString()}`,
						{
							credentials: "include",
							headers: {
								Accept: "application/json",
							},
						}
					);

					if (!response.ok) {
						if (response.status === 401) {
							window.location.href = "/admin/login";
							return;
						}
						throw new Error(
							`HTTP error! status: ${response.status}`
						);
					}

					const data = await response.json();
					if (data.error) {
						throw new Error(data.error);
					}

					displayOrders(data.orders);
					setupPagination(data.currentPage, data.totalPages);
					updateStatsBar(data);
					updateClearFiltersButton();
				} catch (error) {
					console.error("Error fetching orders:", error);
					document.getElementById("ordersList").innerHTML = `
						<div class="alert alert-danger">
							<i class="bi bi-exclamation-triangle me-2"></i>
							Error loading orders: ${error.message}
						</div>
					`;
					showToast(
						"Error",
						"Error loading orders: " + error.message,
						"error"
					);
				}
			}

			function updateStatsBar(data) {
				const statsBar = document.getElementById("statsBar");
				const paginationContainer = document.getElementById(
					"paginationContainer"
				);

				if (data.orders && data.orders.length > 0) {
					const currentResults = data.orders.length;
					const totalResults = data.total || 0;
					const currentPage = data.currentPage || 1;
					const totalPages = data.totalPages || 1;

					document.getElementById("currentResults").textContent =
						currentResults;
					document.getElementById(
						"totalResults"
					).innerHTML = ` of <span class="stats-count">${totalResults}</span> total`;
					document.getElementById("currentPageDisplay").textContent =
						currentPage;
					document.getElementById("totalPagesDisplay").textContent =
						totalPages;

					statsBar.style.display = "flex";
					paginationContainer.style.display =
						totalPages > 1 ? "flex" : "none";
				} else {
					statsBar.style.display = "none";
					paginationContainer.style.display = "none";
				}
			}

			function displayOrders(orders) {
				const ordersListElement = document.getElementById("ordersList");
				const statsBar = document.getElementById("statsBar");
				const paginationContainer = document.getElementById(
					"paginationContainer"
				);

				if (!orders || orders.length === 0) {
					ordersListElement.innerHTML = `
						<div class="empty-state">
							<div class="empty-state-icon">
								<i class="bi bi-inbox"></i>
							</div>
							<div class="empty-state-title">No orders found</div>
							<div class="empty-state-text">
								Try adjusting your search criteria or check back later
							</div>
						</div>
					`;
					statsBar.style.display = "none";
					paginationContainer.style.display = "none";
					return;
				}

				// Helper function to get payment method display info
				function getPaymentMethodHtml(method) {
					const paymentMethods = {
						cod: {
							icon: "cash-stack",
							text: "Cash on Delivery",
						},
						card: {
							icon: "credit-card",
							text: "Credit Card",
						},
						bank: {
							icon: "bank",
							text: "Bank Transfer",
						},
					};

					const paymentInfo = paymentMethods[method] || {
						icon: "question-circle",
						text: "Unknown Payment Method",
					};

					return `<i class="bi bi-${paymentInfo.icon} me-1"></i>${paymentInfo.text}`;
				}

				// Helper function to get status icon and class
				function getStatusInfo(status) {
					const statusMap = {
						pending: { icon: "clock", class: "status-pending" },
						paid: { icon: "check-circle", class: "status-paid" },
						completed: {
							icon: "check-circle-fill",
							class: "status-completed",
						},
						cancelled: {
							icon: "x-circle",
							class: "status-cancelled",
						},
						failed: {
							icon: "exclamation-triangle",
							class: "status-failed",
						},
					};
					return (
						statusMap[status] || {
							icon: "question-circle",
							class: "status-pending",
						}
					);
				}

				// Helper function to format date
				function formatDate(dateString) {
					const date = new Date(dateString);
					return date.toLocaleDateString("en-US", {
						year: "numeric",
						month: "short",
						day: "numeric",
						hour: "2-digit",
						minute: "2-digit",
					});
				}

				ordersListElement.innerHTML = orders
					.map((order) => {
						const statusInfo = getStatusInfo(order.payment_status);
						const formattedDate = formatDate(order.created_at);

						return `
							<div class="order-card">
								<div class="order-card-header">
									<div class="order-id">Order #${order.id}</div>
									<div class="order-date">${formattedDate}</div>
								</div>
								<div class="order-card-body">
									<div class="order-info">
										<div class="info-item">
											<div class="info-label">Customer</div>
											<div class="info-value">
												<i class="bi bi-person-fill me-1"></i>
												${order.customer_name}
											</div>
										</div>
										<div class="info-item">
											<div class="info-label">Email</div>
											<div class="info-value">
												<i class="bi bi-envelope me-1"></i>
												${order.customer_email || "Not provided"}
											</div>
										</div>
										<div class="info-item">
											<div class="info-label">Product</div>
											<div class="info-value">
												<i class="bi bi-box me-1"></i>
												${order.product_name}
											</div>
										</div>
										<div class="info-item">
											<div class="info-label">Total Amount</div>
											<div class="info-value price-value">
												<i class="bi bi-currency-dollar me-1"></i>
												${order.total_price}
											</div>
										</div>
										<div class="info-item">
											<div class="info-label">Payment Method</div>
											<div class="info-value">
												${getPaymentMethodHtml(order.payment_method)}
											</div>
										</div>
										<div class="info-item">
											<div class="info-label">Status</div>
											<div class="info-value">
												<span class="status-badge ${statusInfo.class}">
													<i class="bi bi-${statusInfo.icon}"></i>
													${order.payment_status}
												</span>
											</div>
										</div>
										<div class="info-item">
											<div class="info-label">Shipping Address</div>
											<div class="info-value">
												<i class="bi bi-geo-alt me-1"></i>
												${order.shipping_address || "Not provided"}
											</div>
										</div>
									</div>
									<div class="order-actions">
										<div class="d-flex align-items-center gap-2">
											<label class="info-label mb-0">Update Status:</label>
											<select class="status-select" onchange="updateOrderStatus('${
												order.id
											}', this.value)">
												<option value="pending" ${
													order.payment_status ===
													"pending"
														? "selected"
														: ""
												}>Pending</option>
												<option value="paid" ${
													order.payment_status ===
													"paid"
														? "selected"
														: ""
												}>Paid</option>
												<option value="completed" ${
													order.payment_status ===
													"completed"
														? "selected"
														: ""
												}>Completed</option>
												<option value="cancelled" ${
													order.payment_status ===
													"cancelled"
														? "selected"
														: ""
												}>Cancelled</option>
											</select>
										</div>
										<button class="delete-btn" onclick="deleteOrder('${order.id}')">
											<i class="bi bi-trash"></i>
											Delete Order
										</button>
									</div>
								</div>
							</div>
						`;
					})
					.join("");
			}

			function setupPagination(currentPage, totalPages) {
				const paginationElement = document.getElementById("pagination");
				let paginationHTML = "";

				// Previous button
				paginationHTML += `
			             <li class="page-item ${currentPage === 1 ? "disabled" : ""}">
			                 <a class="page-link" href="#" onclick="changePage(${
									currentPage - 1
								})">Previous</a>
			             </li>
			         `;

				// Page numbers
				for (let i = 1; i <= totalPages; i++) {
					paginationHTML += `
			                 <li class="page-item ${currentPage === i ? "active" : ""}">
			                     <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
			                 </li>
			             `;
				}

				// Next button
				paginationHTML += `
			             <li class="page-item ${
								currentPage === totalPages ? "disabled" : ""
							}">
			                 <a class="page-link" href="#" onclick="changePage(${
									currentPage + 1
								})">Next</a>
			             </li>
			         `;

				paginationElement.innerHTML = paginationHTML;
			}

			async function updateOrderStatus(orderId, newStatus) {
				try {
					// Get CSRF token
					const csrfToken = await getCSRFToken();

					const response = await fetch(
						`/api/orders/${orderId}/status`,
						{
							method: "PUT",
							headers: {
								"Content-Type": "application/json",
								"X-CSRF-Token": csrfToken,
							},
							credentials: "include",
							body: JSON.stringify({ status: newStatus }),
						}
					);

					if (response.status === 401) {
						window.location.href = "/admin/login";
						return;
					}

					const data = await response.json();
					if (data.error) {
						throw new Error(data.error);
					}

					// Refresh the orders list with current filters
					const filters = getCurrentFilters();
					fetchOrders(filters);
					showToast(
						"Success",
						"Order status updated successfully",
						"success"
					);
				} catch (error) {
					console.error("Error updating order status:", error);
					showToast(
						"Error",
						"Error updating order status: " + error.message,
						"error"
					);
				}
			}

			async function deleteOrder(orderId) {
				// Modern confirmation using Bootstrap modal would be better,
				// but for now we'll keep the simple confirm for immediate functionality
				if (
					!confirm(
						"Are you sure you want to delete this order? This action cannot be undone."
					)
				) {
					return;
				}

				try {
					// Get CSRF token
					const csrfToken = await getCSRFToken();

					const response = await fetch(`/admin/orders/${orderId}`, {
						method: "DELETE",
						headers: {
							"X-CSRF-Token": csrfToken,
						},
						credentials: "include",
					});

					if (response.status === 401) {
						window.location.href = "/admin/login";
						return;
					}

					const data = await response.json();
					if (data.error) {
						throw new Error(data.error);
					}

					// Refresh the orders list with current filters
					const filters = getCurrentFilters();
					fetchOrders(filters);
					showToast(
						"Success",
						"Order deleted successfully",
						"success"
					);
				} catch (error) {
					console.error("Error deleting order:", error);
					showToast(
						"Error",
						"Error deleting order: " + error.message,
						"error"
					);
				}
			}

			function changePage(page) {
				const filters = getCurrentFilters();
				filters.page = page;
				currentPage = page;
				console.log("Changing page with filters:", filters);
				fetchOrders(filters);
			}
			function logout() {
				// Clear client-side cookie
				document.cookie =
					"adminToken=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;";

				// Redirect to logout endpoint to clear server-side session
				window.location.href = "/admin/logout";
			}

			// Helper function to format dates consistently
			function getLocalDateString(date) {
				const d = new Date(date);
				const year = d.getFullYear();
				const month = String(d.getMonth() + 1).padStart(2, "0");
				const day = String(d.getDate()).padStart(2, "0");
				return `${year}-${month}-${day}`;
			}

			// Helper function to get today's date
			function getTodayDateString() {
				return getLocalDateString(new Date());
			}

			// Initialize date filters and event handlers
			document.addEventListener("DOMContentLoaded", function () {
				// Set today's date for date filters but don't apply them initially
				const todayStr = getTodayDateString();

				// Set default date values to today (for when user wants to filter)
				document.getElementById("dateFromFilter").value = todayStr;
				document.getElementById("dateToFilter").value = todayStr;

				// Initial load with ALL orders (no date filter)
				fetchOrders({
					page: 1,
					// Don't pass dateFrom and dateTo to show all orders
				});
			});

			// Search input handler
			document.getElementById("searchInput").addEventListener(
				"input",
				debounce(function (e) {
					activeFilters.search = e.target.value;
					currentPage = 1;
					const filters = getCurrentFilters();
					filters.page = 1;
					fetchOrders(filters);
				}, 300)
			);

			// Payment method filter
			document
				.getElementById("paymentMethodFilter")
				.addEventListener("change", function (e) {
					activeFilters.paymentMethod = e.target.value;
					currentPage = 1;
					const filters = getCurrentFilters();
					filters.page = 1;
					fetchOrders(filters);
				});

			// Status filter
			document
				.getElementById("statusFilter")
				.addEventListener("change", function (e) {
					activeFilters.status = e.target.value;
					currentPage = 1;
					const filters = getCurrentFilters();
					filters.page = 1;
					fetchOrders(filters);
				}); // Clear filters button
			document
				.getElementById("clearFilters")
				.addEventListener("click", function () {
					const todayStr = getTodayDateString();

					// Reset all filters to default (empty for all filters)
					document.getElementById("searchInput").value = "";
					document.getElementById("paymentMethodFilter").value = "";
					document.getElementById("statusFilter").value = "";
					document.getElementById("dateFromFilter").value = todayStr;
					document.getElementById("dateToFilter").value = todayStr;

					// Clear active filters
					activeFilters = {
						search: "",
						paymentMethod: "",
						status: "",
						dateFrom: "",
						dateTo: "",
					};

					// Reset to page 1 and fetch ALL orders (no filters)
					currentPage = 1;
					fetchOrders({
						page: 1,
						// Don't pass any filters to show all orders
					});
				});

			// Date filter button
			document
				.getElementById("applyDateFilter")
				.addEventListener("click", function () {
					const dateFrom =
						document.getElementById("dateFromFilter").value;
					const dateTo =
						document.getElementById("dateToFilter").value;
					console.log("Raw date values:", { dateFrom, dateTo });

					if (!dateFrom || !dateTo) {
						showToast(
							"Warning",
							"Please select both start and end dates",
							"warning"
						);
						return;
					}

					const fromDate = new Date(dateFrom);
					const toDate = new Date(dateTo);
					console.log("Parsed date objects:", { fromDate, toDate });

					if (toDate < fromDate) {
						showToast(
							"Warning",
							"End date cannot be earlier than start date",
							"warning"
						);
						return;
					}

					// Add loading state to button
					const button = this;
					button.classList.add("loading");
					button.disabled = true;
					const originalText = button.innerHTML;
					button.innerHTML =
						'<i class="bi bi-hourglass-split"></i> Applying...';

					// Update active filters with current form values
					activeFilters.search =
						document.getElementById("searchInput").value;
					activeFilters.paymentMethod = document.getElementById(
						"paymentMethodFilter"
					).value;
					activeFilters.status =
						document.getElementById("statusFilter").value;
					activeFilters.dateFrom = dateFrom;
					activeFilters.dateTo = dateTo;

					// Reset to page 1 when applying new filters
					currentPage = 1;

					const filters = getCurrentFilters();
					filters.page = 1;

					console.log("Sending filters to fetchOrders:", filters);

					// Fetch orders and then reset button state
					fetchOrders(filters).finally(() => {
						button.classList.remove("loading");
						button.disabled = false;
						button.innerHTML = originalText;
					});
				});

			// Date input change handlers (only validation, no auto-filtering)
			document
				.getElementById("dateFromFilter")
				.addEventListener("change", function (e) {
					const dateTo =
						document.getElementById("dateToFilter").value;
					if (dateTo && new Date(e.target.value) > new Date(dateTo)) {
						showToast(
							"Warning",
							"Start date cannot be later than end date",
							"warning"
						);
						e.target.value = dateTo;
					}
				});

			document
				.getElementById("dateToFilter")
				.addEventListener("change", function (e) {
					const dateFrom =
						document.getElementById("dateFromFilter").value;
					if (
						dateFrom &&
						new Date(e.target.value) < new Date(dateFrom)
					) {
						showToast(
							"Warning",
							"End date cannot be earlier than start date",
							"warning"
						);
						e.target.value = dateFrom;
					}
				});

			// Debounce helper function
			function debounce(func, wait) {
				let timeout;
				return function executedFunction(...args) {
					const later = () => {
						clearTimeout(timeout);
						func(...args);
					};
					clearTimeout(timeout);
					timeout = setTimeout(later, wait);
				};
			}

			// Remove duplicate initial load as it's handled in DOMContentLoaded
		</script>
	</body>
</html>
