# DrxDion E-commerce Platform Environment Variables
# Copy this file to .env and update the values for your environment

# Email Configuration for Notifications
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password
EMAIL_TO=<EMAIL>

# Session Configuration (Change this to a strong secret key!)
SESSION_SECRET=your-super-secure-session-secret-key-change-this-in-production

# Server Configuration
PORT=3000

# Iyzico API Configuration
# For production, use production API credentials from Iyzico dashboard
IYZIPAY_API_KEY=your-production-iyzico-api-key
IYZIPAY_SECRET_KEY=your-production-iyzico-secret-key
IYZIPAY_URI=https://api.iyzipay.com
BASE_URL=https://yourdomain.com

# Google reCAPTCHA v3 Configuration
# Get these from Google reCAPTCHA admin console
RECAPTCHA_SITE_KEY=your-recaptcha-site-key
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key

# Email Configuration for Order Notifications (Admin Only)
ORDER_NOTIFICATION_TO=<EMAIL>
ORDER_NOTIFICATION_FROM=<EMAIL>
ORDER_NOTIFICATION_SUBJECT=Yeni Sipariş - DrxDion

# Note: Customer email notifications are disabled - only admin receives order notifications

# Iyzico Webhook Configuration
# Set a strong secret for webhook security
IYZICO_WEBHOOK_SECRET=your-strong-webhook-secret-key
IYZICO_WEBHOOK_URL=https://yourdomain.com/api/iyzico-webhook

# Environment Settings
NODE_ENV=production
