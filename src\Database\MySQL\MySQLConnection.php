<?php

declare(strict_types=1);

namespace DrxDion\Database\MySQL;

use PDO;
use PDOException;
use RuntimeException;

/**
 * MySQL Database Connection
 * Handles PDO MySQL connection with proper configuration
 */
final class MySQLConnection
{
    private ?PDO $connection = null;
    private string $host;
    private string $database;
    private string $username;
    private string $password;
    private int $port;

    public function __construct(
        string $host = 'localhost',
        string $database = 'drxdion',
        string $username = 'root',
        string $password = '',
        int $port = 3306
    ) {
        $this->host = $host;
        $this->database = $database;
        $this->username = $username;
        $this->password = $password;
        $this->port = $port;
    }

    public function getConnection(): PDO
    {
        if ($this->connection === null) {
            $this->connect();
        }

        return $this->connection;
    }

    private function connect(): void
    {
        try {
            $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->database};charset=utf8mb4";

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $this->connection = new PDO($dsn, $this->username, $this->password, $options);

            // Test the connection
            $this->connection->query('SELECT 1');
        } catch (PDOException $e) {
            throw new RuntimeException(
                "Failed to connect to MySQL database: " . $e->getMessage(),
                (int) $e->getCode(),
                $e
            );
        }
    }

    public function disconnect(): void
    {
        $this->connection = null;
    }

    public function isConnected(): bool
    {
        try {
            if ($this->connection === null) {
                return false;
            }
            return $this->connection->query('SELECT 1') !== false;
        } catch (PDOException $e) {
            return false;
        }
    }

    public function beginTransaction(): bool
    {
        return $this->getConnection()->beginTransaction();
    }

    public function commit(): bool
    {
        return $this->getConnection()->commit();
    }

    public function rollback(): bool
    {
        return $this->getConnection()->rollback();
    }

    public function getLastInsertId(): string
    {
        return $this->getConnection()->lastInsertId();
    }

    public function createTables(): void
    {
        $this->createOrdersTable();
        $this->createAdminUsersTable();
    }

    private function createOrdersTable(): void
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS orders (
                id VARCHAR(100) PRIMARY KEY,
                customer_name VARCHAR(255) NOT NULL,
                customer_email VARCHAR(255) NOT NULL,
                customer_phone VARCHAR(20),
                product_name VARCHAR(255) NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                total_amount DECIMAL(10,2) NOT NULL,
                shipping_cost DECIMAL(10,2) DEFAULT 0.00,
                payment_method VARCHAR(50) NOT NULL,
                payment_status VARCHAR(50) DEFAULT 'pending',
                payment_id VARCHAR(255) NULL,
                shipping_address TEXT NOT NULL,
                items TEXT,
                status VARCHAR(50) DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_payment_status (payment_status),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at),
                INDEX idx_customer_email (customer_email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $this->getConnection()->exec($sql);
    }

    private function createAdminUsersTable(): void
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_email (email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $this->getConnection()->exec($sql);
    }
}
