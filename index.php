<?php

declare(strict_types=1);
/**
 * DrxDion E-commerce Platform
 * Main Entry Point - PHP 8.0 Clean Architecture Version
 */

require_once __DIR__ . '/simple-config.php';

try {
    // Handle the request through the router
    $router->dispatch();
} catch (Throwable $e) {
    error_log('Application Error: ' . $e->getMessage());

    if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true') {
        http_response_code(500);
        header('Content-Type: application/json');
        echo json_encode([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
    } else {
        http_response_code(500);
        echo '<!DOCTYPE html>
<html>
<head>
    <title>Server Error</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .error { color: #d32f2f; }
    </style>
</head>
<body>
    <h1 class="error">Server Error</h1>
    <p>Something went wrong. Please try again later.</p>
</body>
</html>';
    }
}
