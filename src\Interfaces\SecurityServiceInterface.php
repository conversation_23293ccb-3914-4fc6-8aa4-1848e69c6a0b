<?php

declare(strict_types=1);

namespace DrxDion\Interfaces;

/**
 * Security Service Interface
 * Contract for security services
 */
interface SecurityServiceInterface
{
    /**
     * Generate CSRF token
     */
    public function generateCsrfToken(): string;

    /**
     * Verify CSRF token
     */
    public function verifyCsrfToken(string $token): bool;

    /**
     * Hash password
     */
    public function hashPassword(string $password): string;

    /**
     * Verify password
     */
    public function verifyPassword(string $password, string $hash): bool;

    /**
     * Generate secure random string
     */
    public function generateSecureRandom(int $length = 32): string;

    /**
     * Verify reCAPTCHA
     */
    public function verifyRecaptcha(string $response, string $remoteIp): bool;

    /**
     * Check rate limiting
     */
    public function checkRateLimit(string $key, int $maxAttempts, int $windowSeconds): bool;
}
