/**
 * reCA<PERSON><PERSON><PERSON> Loader - Handles fallback and redundancy for reCAP<PERSON>HA loading
 */
(function () {
	// Store our site key for future use
	const RECAPTCHA_SITE_KEY = "6Lcl-mIrAAAAAHBx1-uAMQCuGoWu8NwZLCCcR02o";
	window.RECAPTCHA_SITE_KEY = RECAPTCHA_SITE_KEY;

	// Set initial states
	window.RECAPTCHA_LOADED = false;
	window.RECAPTCHA_ERROR = false;
	window.RECAPTCHA_LOAD_ATTEMPTS = 0;

	// Callback when reCAPTCHA loads successfully
	window.onRecaptchaLoad = function () {
		window.RECAPTCHA_LOADED = true;
	};

	// Error handler for reCAPTCHA script loading
	window.onRecaptchaError = function () {
		window.RECAPTCHA_ERROR = true;
		loadAlternative();
	};
	// Alternative loading method
	function loadAlternative() {
		if (window.RECAPTCHA_LOAD_ATTEMPTS >= 2) {
			window.RECAPTCHA_ERROR = true;
			return;
		}

		window.RECAPTCHA_LOAD_ATTEMPTS++;

		setTimeout(function () {
			// Check if grecaptcha is already loaded
			if (
				typeof grecaptcha !== "undefined" &&
				typeof grecaptcha.execute === "function"
			) {
				window.RECAPTCHA_LOADED = true;
				return;
			}

			// Try with different loading method
			const script = document.createElement("script");
			script.src = `https://www.google.com/recaptcha/api.js?render=${RECAPTCHA_SITE_KEY}`;
			script.async = true;

			script.onload = function () {
				window.RECAPTCHA_LOADED = true;
			};

			script.onerror = function () {
				window.RECAPTCHA_ERROR = true;
			};

			// Add to document
			document.head.appendChild(script);
		}, 1000);
	} // Check status after a delay
	setTimeout(function checkStatus() {
		if (!window.RECAPTCHA_LOADED && !window.RECAPTCHA_ERROR) {
			if (
				typeof grecaptcha === "undefined" ||
				typeof grecaptcha.execute !== "function"
			) {
				loadAlternative();
			} else {
				window.RECAPTCHA_LOADED = true;
			}
		}
	}, 3000);
})();
