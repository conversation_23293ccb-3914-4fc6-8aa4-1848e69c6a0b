<?php

declare(strict_types=1);

/**
 * Test Script for DrxDion PHP Implementation
 * Verify all components are working
 */

echo "🧪 Testing DrxDion PHP Implementation\n";
echo "=====================================\n\n";

require_once __DIR__ . '/../config.php';

$tests = [];
$passed = 0;
$failed = 0;

// Test 1: Environment loading
try {
    $env = \DrxDion\Infrastructure\Config\Environment::get('app.name');
    $tests[] = ['Environment Loading', $env === 'DrxDion E-commerce Platform', $env];
} catch (\Exception $e) {
    $tests[] = ['Environment Loading', false, $e->getMessage()];
}

// Test 2: DI Container
try {
    $testContainer = new \DrxDion\Infrastructure\DependencyInjection\Container();
    $testContainer->bind('test', function () {
        return 'works';
    });
    $result = $testContainer->get('test');
    $tests[] = ['DI Container', $result === 'works', $result];
} catch (\Exception $e) {
    $tests[] = ['DI Container', false, $e->getMessage()];
}

// Test 3: Database Connection
try {
    $database = $container->get(\DrxDion\Infrastructure\Persistence\SQLite\DatabaseConnection::class);
    $connection = $database->getConnection();
    $stmt = $connection->query("SELECT 1");
    $result = $stmt->fetchColumn();
    $tests[] = ['Database Connection', $result == 1, 'Connected'];
} catch (\Exception $e) {
    $tests[] = ['Database Connection', false, $e->getMessage()];
}

// Test 4: Order Repository
try {
    $orderRepo = $container->get(\DrxDion\Domain\Contracts\OrderRepositoryInterface::class);
    $count = $orderRepo->count();
    $tests[] = ['Order Repository', is_int($count), "Orders: $count"];
} catch (\Exception $e) {
    $tests[] = ['Order Repository', false, $e->getMessage()];
}

// Test 5: Admin Repository
try {
    $adminRepo = $container->get(\DrxDion\Domain\Contracts\AdminRepositoryInterface::class);
    $hasAdmins = $adminRepo->hasAnyAdmins();
    $tests[] = ['Admin Repository', is_bool($hasAdmins), $hasAdmins ? 'Has admins' : 'No admins'];
} catch (\Exception $e) {
    $tests[] = ['Admin Repository', false, $e->getMessage()];
}

// Test 6: Security Service
try {
    $securityService = $container->get(\DrxDion\Domain\Contracts\SecurityServiceInterface::class);
    $token = $securityService->generateCsrfToken();
    $tests[] = ['Security Service', !empty($token), 'Token generated'];
} catch (\Exception $e) {
    $tests[] = ['Security Service', false, $e->getMessage()];
}

// Display results
foreach ($tests as $test) {
    [$name, $success, $details] = $test;
    $status = $success ? '✅' : '❌';
    echo sprintf("%-20s %s %s\n", $name, $status, $details);

    if ($success) {
        $passed++;
    } else {
        $failed++;
    }
}

echo "\n📊 Test Summary:\n";
echo "✅ Passed: $passed\n";
echo "❌ Failed: $failed\n";
echo "📈 Success Rate: " . round(($passed / count($tests)) * 100, 2) . "%\n\n";

if ($failed === 0) {
    echo "🎉 All tests passed! The PHP implementation is working correctly.\n";
    echo "🚀 You can now start the development server with:\n";
    echo "   php -S localhost:8000 -t . index.php\n";
} else {
    echo "⚠️  Some tests failed. Please check the errors above.\n";
    echo "💡 Try running: php scripts/setup.php\n";
}

echo "\n🔗 Quick Links:\n";
echo "- Main site: http://localhost:8000/\n";
echo "- Admin login: http://localhost:8000/admin/login\n";
echo "- Health check: http://localhost:8000/health\n";
