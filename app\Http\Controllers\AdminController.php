<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Settings;
use App\Models\AdminUser;
use App\Services\StorageService;

/**
 * Admin Controller
 * Handles admin panel API endpoints
 */
final class AdminController extends Controller
{
    private StorageService $storageService;

    public function __construct(StorageService $storageService)
    {
        $this->storageService = $storageService;
    }

    public function getSettings(): array
    {
        try {
            $settings = Settings::getAll();

            // Convert to simple key-value format for frontend
            $settingsData = [];
            foreach ($settings as $key => $value) {
                $settingsData[$key] = $value;
            }

            // Provide default values if settings don't exist
            $defaultSettings = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'notification_email' => '<EMAIL>',
                'site_name' => 'DrxDion',
                'timezone' => 'Europe/Istanbul'
            ];

            $result = array_merge($defaultSettings, $settingsData);

            return [
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to load settings: ' . $e->getMessage()
            ];
        }
    }

    public function updateSettings(array $data): array
    {
        try {
            if (empty($data)) {
                return [
                    'success' => false,
                    'error' => 'No data provided'
                ];
            }

            // Handle password update separately if provided
            if (!empty($data['new_password'])) {
                if (empty($data['current_password'])) {
                    return [
                        'success' => false,
                        'error' => 'Current password is required to change password'
                    ];
                }

                // Verify current password
                $currentUser = AdminUser::findByUsername($data['username'] ?? 'admin');
                if (!$currentUser || !password_verify($data['current_password'], $currentUser->getPasswordHash())) {
                    return [
                        'success' => false,
                        'error' => 'Current password is incorrect'
                    ];
                }

                // Update password
                $hashedPassword = password_hash($data['new_password'], PASSWORD_DEFAULT);
                $currentUser->updatePassword($hashedPassword);
            }

            // Update other settings
            $settingsToUpdate = [];
            $allowedSettings = ['username', 'email', 'notification_email', 'site_name', 'timezone'];

            foreach ($allowedSettings as $key) {
                if (isset($data[$key])) {
                    $settingsToUpdate[$key] = $data[$key];
                }
            }

            if (!empty($settingsToUpdate)) {
                Settings::updateMultiple($settingsToUpdate);
            }

            return [
                'success' => true,
                'message' => 'Settings updated successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to update settings: ' . $e->getMessage()
            ];
        }
    }

    public function getStorageStatus(): array
    {
        try {
            $status = $this->storageService->getStatus();
            $healthMetrics = $this->storageService->getHealthMetrics();
            $syncStats = $this->storageService->getSyncStats();

            return [
                'success' => true,
                'data' => [
                    'status' => $status,
                    'health' => $healthMetrics,
                    'sync' => $syncStats
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get storage status: ' . $e->getMessage()
            ];
        }
    }

    public function syncStorage(array $data): array
    {
        try {
            // CSRF protection
            if (!$this->validateCSRF()) {
                return [
                    'success' => false,
                    'error' => 'Invalid CSRF token'
                ];
            }

            $forceReplace = ($data['force_replace'] ?? false) === true;

            $result = $this->storageService->sync($forceReplace);

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'Storage sync completed successfully',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error'] ?? 'Sync failed',
                    'data' => $result
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to sync storage: ' . $e->getMessage()
            ];
        }
    }

    private function validateCSRF(): bool
    {
        $providedToken = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        $sessionToken = $_SESSION['csrf_token'] ?? '';

        return !empty($providedToken) && !empty($sessionToken) && hash_equals($sessionToken, $providedToken);
    }
}
