<?php

declare(strict_types=1);

namespace App\Http\Controllers;

/**
 * Page Controller
 * Handles public page requests
 */
final class PageController extends Controller
{
    public function index(): void
    {
        // Serve the main index.html file
        $indexPath = BASE_PATH . '/public/index.html';

        if (file_exists($indexPath)) {
            header('Content-Type: text/html; charset=utf-8');
            readfile($indexPath);
        } else {
            http_response_code(404);
            echo 'Page not found';
        }
    }

    public function adminLogin(): void
    {
        // Debug: Simple output first
        echo "AdminLogin method called<br>";
        echo "BASE_PATH: " . BASE_PATH . "<br>";

        // If already logged in, redirect to orders
        if ($this->isAdminLoggedIn()) {
            echo "User already logged in, redirecting...<br>";
            header('Location: /admin/orders');
            exit;
        }

        // Debug: Check if view file exists
        $viewPath = BASE_PATH . '/resources/views/admin/login.ejs';
        echo "Looking for view at: " . $viewPath . "<br>";

        if (!file_exists($viewPath)) {
            echo "View file not found: " . $viewPath;
            return;
        }

        echo "View file exists, rendering...<br>";
        $this->renderView('admin/login.ejs');
    }

    public function adminOrders(): void
    {
        // Check if admin is logged in
        if (!$this->isAdminLoggedIn()) {
            header('Location: /admin/login');
            exit;
        }

        $this->renderView('admin/orders.ejs');
    }

    public function adminSettings(): void
    {
        // Check if admin is logged in
        if (!$this->isAdminLoggedIn()) {
            header('Location: /admin/login');
            exit;
        }

        $this->renderView('admin/settings.ejs');
    }

    public function adminRoot(): void
    {
        // Check if admin is logged in
        if ($this->isAdminLoggedIn()) {
            // Redirect to admin dashboard/orders
            header('Location: /admin/orders');
            exit;
        } else {
            // Redirect to login page
            header('Location: /admin/login');
            exit;
        }
    }

    public function debug(): void
    {
        $debugPath = BASE_PATH . '/public/debug.php';

        if (file_exists($debugPath)) {
            header('Content-Type: text/html; charset=utf-8');
            include $debugPath;
        } else {
            echo "Debug file not found!";
        }
    }

    public function test(): void
    {
        echo "Test route is working!<br>";
        echo "BASE_PATH: " . BASE_PATH . "<br>";
        echo "Session status: " . session_status() . "<br>";
        echo "Admin logged in: " . ($this->isAdminLoggedIn() ? 'Yes' : 'No') . "<br>";
    }

    private function renderView(string $view): void
    {
        $viewPath = BASE_PATH . '/resources/views/' . $view;

        if (file_exists($viewPath)) {
            header('Content-Type: text/html; charset=utf-8');
            readfile($viewPath);
        } else {
            http_response_code(404);
            echo 'View not found: ' . $view;
        }
    }

    private function isAdminLoggedIn(): bool
    {
        return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
    }
}
