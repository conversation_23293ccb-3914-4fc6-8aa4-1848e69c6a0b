<?php

declare(strict_types=1);

namespace App\Http\Controllers;

/**
 * Page Controller
 * Handles public page requests
 */
final class PageController extends Controller
{
    public function index(): void
    {
        // Serve the main index.html file
        $indexPath = BASE_PATH . '/public/index.html';

        if (file_exists($indexPath)) {
            header('Content-Type: text/html; charset=utf-8');
            readfile($indexPath);
        } else {
            http_response_code(404);
            echo 'Page not found';
        }
    }

    public function adminLogin(): void
    {
        // If already logged in, redirect to orders
        if ($this->isAdminLoggedIn()) {
            header('Location: /admin/orders');
            exit;
        }

        $this->renderView('admin/login.ejs');
    }

    public function adminOrders(): void
    {
        // Check if admin is logged in
        if (!$this->isAdminLoggedIn()) {
            header('Location: /admin/login');
            exit;
        }

        $this->renderView('admin/orders.ejs');
    }

    public function adminSettings(): void
    {
        // Check if admin is logged in
        if (!$this->isAdminLoggedIn()) {
            header('Location: /admin/login');
            exit;
        }

        $this->renderView('admin/settings.ejs');
    }

    public function adminRoot(): void
    {
        // Check if admin is logged in
        if ($this->isAdminLoggedIn()) {
            // Redirect to admin dashboard/orders
            header('Location: /admin/orders');
            exit;
        } else {
            // Redirect to login page
            header('Location: /admin/login');
            exit;
        }
    }

    public function debug(): void
    {
        $debugPath = BASE_PATH . '/public/debug.php';

        if (file_exists($debugPath)) {
            header('Content-Type: text/html; charset=utf-8');
            include $debugPath;
        } else {
            echo "Debug file not found!";
        }
    }



    private function renderView(string $view): void
    {
        $viewPath = BASE_PATH . '/resources/views/' . $view;

        if (file_exists($viewPath)) {
            header('Content-Type: text/html; charset=utf-8');
            readfile($viewPath);
        } else {
            http_response_code(404);
            echo 'View not found: ' . $view;
        }
    }

    private function isAdminLoggedIn(): bool
    {
        return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
    }
}
