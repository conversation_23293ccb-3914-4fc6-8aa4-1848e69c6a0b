<?php

declare(strict_types=1);

/**
 * Laravel-style Entry Point
 * DrxDion E-commerce Platform
 */

// Bootstrap the application
$container = require_once __DIR__ . '/../bootstrap/app.php';

// Simple Router Class
class Router
{
    private array $routes = [];
    private $container;

    public function __construct($container)
    {
        $this->container = $container;
        $this->loadRoutes();
    }

    private function loadRoutes(): void
    {
        // Load web routes
        $webRoutes = require BASE_PATH . '/routes/web.php';
        foreach ($webRoutes as $uri => $route) {
            $this->routes[$uri] = $route;
        }

        // Load API routes
        $apiRoutes = require BASE_PATH . '/routes/api.php';
        foreach ($apiRoutes as $uri => $route) {
            $this->routes[$uri] = $route;
        }
    }

    public function dispatch(): void
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

        // Remove query string
        $uri = strtok($uri, '?');

        // Try exact match first
        if (isset($this->routes[$uri])) {
            $route = $this->routes[$uri];
            if ($this->methodMatches($method, $route[0])) {
                $this->callController($route[1]);
                return;
            }
        }

        // Try pattern matching for dynamic routes
        foreach ($this->routes as $pattern => $route) {
            if ($this->patternMatches($uri, $pattern) && $this->methodMatches($method, $route[0])) {
                $this->callController($route[1]);
                return;
            }
        }

        // Fallback to legacy simple-config for now
        $this->handleLegacyRouting($uri, $method);
    }

    private function methodMatches(string $method, string $allowedMethods): bool
    {
        return in_array($method, explode('|', $allowedMethods));
    }

    private function patternMatches(string $uri, string $pattern): bool
    {
        $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $pattern);
        return preg_match('#^' . $pattern . '$#', $uri);
    }

    private function callController(string $controllerAction): void
    {
        [$controller, $method] = explode('@', $controllerAction);

        try {
            $controllerInstance = new $controller();
            $controllerInstance->$method();
        } catch (\Exception $e) {
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Controller error: ' . $e->getMessage()
            ]);
        }
    }

    private function handleLegacyRouting(string $uri, string $method): void
    {
        // Include the legacy simple-config router for backward compatibility
        require_once BASE_PATH . '/simple-config.php';
    }
}

// Create and dispatch the router
$router = new Router($container);
$router->dispatch();
