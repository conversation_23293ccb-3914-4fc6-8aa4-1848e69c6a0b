<?php

declare(strict_types=1);

namespace DrxDion\Interfaces;

use DrxDion\Models\Order;

/**
 * Order Repository Interface
 * Contract for order data persistence
 */
interface OrderRepositoryInterface
{
    /**
     * Save an order
     */
    public function save(Order $order): void;

    /**
     * Find order by ID
     */
    public function findById(string $id): ?Order;

    /**
     * Find all orders with optional pagination
     */
    public function findAll(int $limit = 100, int $offset = 0): array;

    /**
     * Update order status
     */
    public function updateStatus(string $id, string $status): bool;

    /**
     * Update order payment information
     */
    public function updatePayment(string $id, ?string $paymentId, string $status): bool;

    /**
     * Delete an order
     */
    public function delete(string $id): bool;

    /**
     * Count total orders
     */
    public function count(): int;

    /**
     * Count total orders with criteria
     */
    public function countWithCriteria(array $criteria): int;

    /**
     * Search orders by criteria
     */
    public function search(array $criteria, int $limit = 100, int $offset = 0): array;
}
