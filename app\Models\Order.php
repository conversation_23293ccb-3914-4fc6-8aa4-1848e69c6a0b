<?php

declare(strict_types=1);

namespace App\Models;

use DateTimeImmutable;

/**
 * Order Entity
 * Core business entity representing an order
 */
final class Order
{
    private string $id;
    private string $productName;
    private float $price;
    private float $totalPrice;
    private float $shippingCost;
    private string $paymentMethod;
    private string $paymentStatus;
    private ?string $paymentId;
    private string $customerName;
    private string $customerEmail;
    private string $shippingAddress;
    private DateTimeImmutable $createdAt;
    private DateTimeImmutable $updatedAt;

    public function __construct(
        string $id,
        string $productName,
        float $price,
        float $totalPrice,
        float $shippingCost,
        string $paymentMethod,
        string $paymentStatus,
        ?string $paymentId,
        string $customerName,
        string $customerEmail,
        string $shippingAddress,
        DateTimeImmutable $createdAt,
        DateTimeImmutable $updatedAt
    ) {
        $this->id = $id;
        $this->productName = $productName;
        $this->price = $price;
        $this->totalPrice = $totalPrice;
        $this->shippingCost = $shippingCost;
        $this->paymentMethod = $paymentMethod;
        $this->paymentStatus = $paymentStatus;
        $this->paymentId = $paymentId;
        $this->customerName = $customerName;
        $this->customerEmail = $customerEmail;
        $this->shippingAddress = $shippingAddress;
        $this->createdAt = $createdAt;
        $this->updatedAt = $updatedAt;
    }

    public static function create(
        string $id,
        string $productName,
        float $price,
        float $totalPrice,
        float $shippingCost,
        string $paymentMethod,
        string $customerName,
        string $customerEmail,
        string $shippingAddress
    ): self {
        $now = new DateTimeImmutable();

        return new self(
            $id,
            $productName,
            $price,
            $totalPrice,
            $shippingCost,
            $paymentMethod,
            'pending',
            null,
            $customerName,
            $customerEmail,
            $shippingAddress,
            $now,
            $now
        );
    }

    public function updatePaymentStatus(string $status, ?string $paymentId = null): self
    {
        return new self(
            $this->id,
            $this->productName,
            $this->price,
            $this->totalPrice,
            $this->shippingCost,
            $this->paymentMethod,
            $status,
            $paymentId ?? $this->paymentId,
            $this->customerName,
            $this->customerEmail,
            $this->shippingAddress,
            $this->createdAt,
            new DateTimeImmutable()
        );
    }

    public static function getAll(): array
    {
        try {
            $config = require dirname(__DIR__, 2) . '/config/database.php';
            $mysql = $config['connections']['mysql'];

            $dsn = "mysql:host={$mysql['host']};port={$mysql['port']};dbname={$mysql['database']};charset=utf8mb4";
            $pdo = new \PDO($dsn, $mysql['username'], $mysql['password'], [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false
            ]);

            $stmt = $pdo->query('SELECT * FROM orders ORDER BY created_at DESC');
            $orders = [];

            while ($row = $stmt->fetch()) {
                $orders[] = [
                    'id' => $row['id'],
                    'product_name' => $row['product_name'],
                    'price' => (float) $row['price'],
                    'total_price' => (float) $row['total_price'],
                    'shipping_cost' => (float) $row['shipping_cost'],
                    'payment_method' => $row['payment_method'],
                    'payment_status' => $row['payment_status'],
                    'payment_id' => $row['payment_id'],
                    'customer_name' => $row['customer_name'],
                    'customer_email' => $row['customer_email'],
                    'shipping_address' => $row['shipping_address'],
                    'created_at' => $row['created_at'],
                    'updated_at' => $row['updated_at']
                ];
            }

            return $orders;
        } catch (\Exception $e) {
            // Fallback to JSON if MySQL fails
            return self::getFromJSON();
        }
    }

    private static function getFromJSON(): array
    {
        $jsonFile = dirname(__DIR__, 2) . '/data/orders.json';

        if (!file_exists($jsonFile)) {
            return [];
        }

        $content = file_get_contents($jsonFile);
        if (empty($content)) {
            return [];
        }

        $data = json_decode($content, true);
        return $data ?? [];
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getProductName(): string
    {
        return $this->productName;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function getTotalPrice(): float
    {
        return $this->totalPrice;
    }

    public function getShippingCost(): float
    {
        return $this->shippingCost;
    }

    public function getPaymentMethod(): string
    {
        return $this->paymentMethod;
    }

    public function getPaymentStatus(): string
    {
        return $this->paymentStatus;
    }

    public function getPaymentId(): ?string
    {
        return $this->paymentId;
    }

    public function getCustomerName(): string
    {
        return $this->customerName;
    }

    public function getCustomerEmail(): string
    {
        return $this->customerEmail;
    }

    public function getShippingAddress(): string
    {
        return $this->shippingAddress;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'product_name' => $this->productName,
            'price' => $this->price,
            'total_price' => $this->totalPrice,
            'shipping_cost' => $this->shippingCost,
            'payment_method' => $this->paymentMethod,
            'payment_status' => $this->paymentStatus,
            'payment_id' => $this->paymentId,
            'customer_name' => $this->customerName,
            'customer_email' => $this->customerEmail,
            'shipping_address' => $this->shippingAddress,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s'),
        ];
    }
}
