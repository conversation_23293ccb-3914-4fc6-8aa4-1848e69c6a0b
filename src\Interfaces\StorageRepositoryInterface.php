<?php

declare(strict_types=1);

namespace DrxDion\Interfaces;

/**
 * Storage Repository Interface
 * Defines methods for storage monitoring and management
 */
interface StorageRepositoryInterface
{
    /**
     * Get storage status for all repositories
     */
    public function getStatus(): array;

    /**
     * Get synchronization statistics
     */
    public function getSyncStats(): array;

    /**
     * Perform synchronization between storage systems
     */
    public function sync(bool $forceReplace = false): array;

    /**
     * Get storage health metrics
     */
    public function getHealthMetrics(): array;

    /**
     * Get last sync timestamp
     */
    public function getLastSyncTime(): ?string;
}
