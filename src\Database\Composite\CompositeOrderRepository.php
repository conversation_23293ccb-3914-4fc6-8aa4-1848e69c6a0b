<?php

declare(strict_types=1);

namespace DrxDion\Database\Composite;

use DrxDion\Interfaces\OrderRepositoryInterface;
use DrxDion\Models\Order;
use DrxDion\Infrastructure\Persistence\MySQL\MySQLOrderRepository;
use DrxDion\Infrastructure\Persistence\File\JsonOrderRepository;

/**
 * Composite Order Repository
 * Saves orders to both MySQL and JSON files
 */
final class CompositeOrderRepository implements OrderRepositoryInterface
{
    public function __construct(
        private MySQLOrderRepository $mysqlRepository,
        private JsonOrderRepository $jsonRepository
    ) {}

    public function save(Order $order): void
    {
        // Save to MySQL first (primary storage)
        $this->mysqlRepository->save($order);

        // Then save to JSON as backup/archive
        try {
            $this->jsonRepository->save($order);
        } catch (\Exception $e) {
            // Log JSON save error but don't fail the whole operation
            error_log("Failed to save order to JSON: " . $e->getMessage());
        }
    }

    public function findById(string $id): ?Order
    {
        // Use MySQL as primary source
        return $this->mysqlRepository->findById($id);
    }

    public function findAll(int $limit = 100, int $offset = 0): array
    {
        // Use MySQL as primary source
        return $this->mysqlRepository->findAll($limit, $offset);
    }

    public function updateStatus(string $id, string $status): bool
    {
        // Update in MySQL first
        $mysqlSuccess = $this->mysqlRepository->updateStatus($id, $status);

        if ($mysqlSuccess) {
            // Try to update JSON as well
            try {
                $this->jsonRepository->updateStatus($id, $status);
            } catch (\Exception $e) {
                error_log("Failed to update order status in JSON: " . $e->getMessage());
            }
        }

        return $mysqlSuccess;
    }

    public function updatePayment(string $id, ?string $paymentId, string $status): bool
    {
        // Update in MySQL first
        $mysqlSuccess = $this->mysqlRepository->updatePayment($id, $paymentId, $status);

        if ($mysqlSuccess) {
            // Try to update JSON as well
            try {
                $this->jsonRepository->updatePayment($id, $paymentId, $status);
            } catch (\Exception $e) {
                error_log("Failed to update order payment in JSON: " . $e->getMessage());
            }
        }

        return $mysqlSuccess;
    }

    public function delete(string $id): bool
    {
        // Delete from MySQL first
        $mysqlSuccess = $this->mysqlRepository->delete($id);

        if ($mysqlSuccess) {
            // Try to delete from JSON as well
            try {
                $this->jsonRepository->delete($id);
            } catch (\Exception $e) {
                error_log("Failed to delete order from JSON: " . $e->getMessage());
            }
        }

        return $mysqlSuccess;
    }

    public function count(): int
    {
        // Use MySQL as primary source
        return $this->mysqlRepository->count();
    }

    public function countWithCriteria(array $criteria): int
    {
        // Use MySQL as primary source
        return $this->mysqlRepository->countWithCriteria($criteria);
    }

    public function search(array $criteria, int $limit = 20, int $offset = 0): array
    {
        // Use MySQL as primary source
        return $this->mysqlRepository->search($criteria, $limit, $offset);
    }

    public function findByStatus(string $status, int $limit = 100): array
    {
        // Use MySQL as primary source
        return $this->mysqlRepository->findByStatus($status, $limit);
    }

    public function countByStatus(string $status): int
    {
        // Use MySQL as primary source
        return $this->mysqlRepository->countByStatus($status);
    }

    public function countAll(): int
    {
        // Use MySQL as primary source
        return $this->mysqlRepository->countAll();
    }
}
