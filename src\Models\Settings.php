<?php

declare(strict_types=1);

namespace DrxDion\Models;

/**
 * Settings Model
 * Represents application settings
 */
final class Settings
{
    public function __construct(
        private readonly string $key,
        private readonly string $value,
        private readonly string $description = '',
        private readonly string $type = 'string',
        private readonly ?string $category = null,
        private readonly ?int $id = null,
        private readonly ?\DateTimeImmutable $updatedAt = null,
        private readonly ?\DateTimeImmutable $createdAt = null
    ) {}

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getKey(): string
    {
        return $this->key;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'key' => $this->key,
            'value' => $this->value,
            'description' => $this->description,
            'type' => $this->type,
            'category' => $this->category,
            'created_at' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt?->format('Y-m-d H:i:s')
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            key: $data['key'] ?? '',
            value: $data['value'] ?? '',
            description: $data['description'] ?? '',
            type: $data['type'] ?? 'string',
            category: $data['category'] ?? null,
            id: isset($data['id']) ? (int) $data['id'] : null,
            createdAt: isset($data['created_at']) ?
                new \DateTimeImmutable($data['created_at']) : null,
            updatedAt: isset($data['updated_at']) ?
                new \DateTimeImmutable($data['updated_at']) : null
        );
    }
}
