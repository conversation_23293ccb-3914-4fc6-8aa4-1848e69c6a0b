# Deployment Guide - DrxDion E-commerce Platform

## 🚀 Production Deployment Checklist

### Pre-Deployment

- [ ] Update environment variables for production
- [ ] Test all features locally
- [ ] Backup current database
- [ ] Update Iyzico settings to production mode
- [ ] Configure SSL certificate
- [ ] Set up domain and DNS

### Environment Configuration

1. **Create production .env file**:
   ```bash
   NODE_ENV=production
   PORT=3000
   BASE_URL=https://yourdomain.com
   SESSION_SECRET=your-super-secure-session-secret
   
   # Use production Iyzico credentials
   IYZIPAY_API_KEY=your-production-api-key
   IYZIPAY_SECRET_KEY=your-production-secret-key
   IYZIPAY_URI=https://api.iyzipay.com
   
   # Production email settings
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   EMAIL_TO=<EMAIL>
     # Production reCAPTCHA keys
   RECAPTCHA_SITE_KEY=your-production-site-key
   RECAPTCHA_SECRET_KEY=your-production-secret-key
   ```

### Server Setup (Ubuntu/CentOS)

1. **Install Node.js and npm**:
   ```bash
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

2. **Install PM2 for process management**:
   ```bash
   sudo npm install -g pm2
   ```

3. **Clone and setup application**:
   ```bash
   git clone <your-repo-url>
   cd drxdion
   npm install --production
   ```

4. **Configure PM2**:
   ```bash
   # Create PM2 ecosystem file
   cat > ecosystem.config.js << EOF
   module.exports = {
     apps: [{
       name: 'drxdion',
       script: 'server.js',
       instances: 'max',
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'development'
       },
       env_production: {
         NODE_ENV: 'production',
         PORT: 3000
       }
     }]
   }
   EOF
   
   # Start with PM2
   pm2 start ecosystem.config.js --env production
   pm2 save
   pm2 startup
   ```

### Nginx Configuration

1. **Install Nginx**:
   ```bash
   sudo apt update
   sudo apt install nginx
   ```

2. **Configure Nginx**:
   ```bash
   sudo nano /etc/nginx/sites-available/drxdion
   ```

   ```nginx
   server {
       listen 80;
       server_name yourdomain.com www.yourdomain.com;
       return 301 https://$server_name$request_uri;
   }

   server {
       listen 443 ssl http2;
       server_name yourdomain.com www.yourdomain.com;

       ssl_certificate /path/to/your/certificate.crt;
       ssl_certificate_key /path/to/your/private.key;

       # Security headers
       add_header X-Frame-Options "SAMEORIGIN" always;
       add_header X-XSS-Protection "1; mode=block" always;
       add_header X-Content-Type-Options "nosniff" always;
       add_header Referrer-Policy "no-referrer-when-downgrade" always;
       add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }

       # Static files
       location /assets/ {
           alias /path/to/drxdion/public/assets/;
           expires 1y;
           add_header Cache-Control "public, immutable";
       }

       # File upload size
       client_max_body_size 10M;
   }
   ```

3. **Enable site**:
   ```bash
   sudo ln -s /etc/nginx/sites-available/drxdion /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### SSL Certificate (Let's Encrypt)

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### Database Backup Script

```bash
#!/bin/bash
# backup-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"
DB_PATH="/path/to/drxdion/db/orders.db"

mkdir -p $BACKUP_DIR
cp $DB_PATH $BACKUP_DIR/orders_$DATE.db

# Keep only last 30 backups
ls -t $BACKUP_DIR/orders_*.db | tail -n +31 | xargs rm -f

echo "Database backed up to: $BACKUP_DIR/orders_$DATE.db"
```

Set up cron job for automatic backups:
```bash
crontab -e
# Add this line for daily backups at 2 AM
0 2 * * * /path/to/backup-db.sh
```

### Firewall Configuration

```bash
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### Monitoring and Logs

1. **Monitor with PM2**:
   ```bash
   pm2 monit
   pm2 logs drxdion
   pm2 status
   ```

2. **Log rotation**:
   ```bash
   pm2 install pm2-logrotate
   ```

### Health Check Script

```bash
#!/bin/bash
# health-check.sh

URL="https://yourdomain.com"
STATUS=$(curl -s -o /dev/null -w "%{http_code}" $URL)

if [ $STATUS -eq 200 ]; then
    echo "✅ Site is healthy"
else
    echo "❌ Site is down (Status: $STATUS)"
    # Restart application
    pm2 restart drxdion
    # Send notification (optional)
    # curl -X POST -H 'Content-type: application/json' --data '{"text":"DrxDion site is down!"}' YOUR_SLACK_WEBHOOK_URL
fi
```

### Performance Optimization

1. **Enable gzip in Nginx**:
   ```nginx
   gzip on;
   gzip_vary on;
   gzip_min_length 1024;
   gzip_types text/plain application/json application/javascript text/css text/xml application/xml;
   ```

2. **PM2 configuration for clustering**:
   ```javascript
   // ecosystem.config.js
   module.exports = {
     apps: [{
       name: 'drxdion',
       script: 'server.js',
       instances: 'max', // Use all CPU cores
       exec_mode: 'cluster'
     }]
   }
   ```

## 🔧 Maintenance

### Regular Tasks

1. **Update dependencies**:
   ```bash
   npm audit
   npm update
   ```

2. **Database maintenance**:
   ```bash
   npm run backup-db
   # Vacuum database occasionally
   sqlite3 db/orders.db "VACUUM;"
   ```

3. **Log management**:
   ```bash
   pm2 flush  # Clear logs
   ```

4. **Security updates**:
   ```bash
   sudo apt update && sudo apt upgrade
   ```

### Troubleshooting

1. **Application won't start**:
   ```bash
   pm2 logs drxdion
   pm2 restart drxdion
   ```

2. **Database locked**:
   ```bash
   pm2 stop drxdion
   # Wait a few seconds
   pm2 start drxdion
   ```

3. **High memory usage**:
   ```bash
   pm2 restart drxdion
   pm2 monit
   ```

## 📊 Monitoring

### Key Metrics to Monitor

- Response time
- Error rate
- Database size
- Memory usage
- CPU usage
- Disk space

### Recommended Tools

- **PM2**: Process monitoring
- **Nginx access logs**: Traffic analysis
- **New Relic**: Application performance monitoring
- **UptimeRobot**: Uptime monitoring
- **LogRocket**: User session recording

This deployment guide ensures your DrxDion e-commerce platform runs smoothly in production with proper security, monitoring, and maintenance procedures.
