<?php

declare(strict_types=1);

/**
 * Test Laravel-style API endpoints
 */

// Test the API endpoints
$baseUrl = 'http://localhost/drxdion';
$endpoints = [
    '/api/get-settings',
    '/api/orders',
    '/api/admin/storage/status'
];

foreach ($endpoints as $endpoint) {
    echo "Testing: $endpoint\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    echo "Response: " . substr($response, 0, 200) . "...\n";
    echo "---\n";
}

echo "Test completed!\n";
