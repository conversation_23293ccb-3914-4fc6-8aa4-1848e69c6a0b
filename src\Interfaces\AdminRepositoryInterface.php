<?php

declare(strict_types=1);

namespace DrxDion\Interfaces;

use DrxDion\Models\AdminUser;

/**
 * Admin Repository Interface
 * Contract for admin user data persistence
 */
interface AdminRepositoryInterface
{
    /**
     * Save an admin user
     */
    public function save(AdminUser $admin): void;

    /**
     * Find admin by username
     */
    public function findByUsername(string $username): ?AdminUser;

    /**
     * Find admin by ID
     */
    public function findById(int $id): ?AdminUser;

    /**
     * Check if any admin users exist
     */
    public function hasAnyAdmins(): bool;

    /**
     * Create default admin user
     */
    public function createDefaultAdmin(string $username, string $password, string $email): AdminUser;

    /**
     * Validate admin credentials
     */
    public function validateCredentials(string $username, string $password): ?AdminUser;

    /**
     * Update admin password
     */
    public function updatePassword(int $id, string $newPasswordHash): bool;
}
