<?php

declare(strict_types=1);

/**
 * Simplified DrxDion Bootstrap
 */

// Simple debug output
error_log("STARTING SIMPLE-CONFIG.PHP");

// Composer autoloader
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
}

// Simple autoloader
spl_autoload_register(function (string $class): void {
    $prefix = 'DrxDion\\';
    $baseDir = __DIR__ . '/src/';

    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }

    $relativeClass = substr($class, $len);
    $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';

    if (file_exists($file)) {
        require_once $file;
    }
});

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $envContent = file_get_contents(__DIR__ . '/.env');
    $envLines = explode("\n", $envContent);
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || str_starts_with($line, '#')) {
            continue;
        }
        if (str_contains($line, '=')) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Start session for admin authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize dependency injection container
use DrxDion\Config\Container;

$container = new Container();

// Register basic services
$container->bind('db_config', function () {
    return [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'dbname' => $_ENV['DB_NAME'] ?? 'drxdion',
        'username' => $_ENV['DB_USER'] ?? 'root',
        'password' => $_ENV['DB_PASS'] ?? '',
    ];
});

// Simple router
class SimpleRouter
{
    private $container;

    public function __construct($container)
    {
        $this->container = $container;
    }

    public function dispatch()
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

        // Remove query string
        $uri = strtok($uri, '?');

        // Basic routing
        if ($uri === '/' || $uri === '/index.php') {
            $this->servePublicPage();
        } elseif (str_starts_with($uri, '/api/')) {
            $this->handleApiRequest($uri, $method);
        } elseif ($uri === '/admin' || $uri === '/admin/') {
            $this->handleAdminRoot();
        } elseif (str_starts_with($uri, '/admin/')) {
            $this->handleAdminRequest($uri, $method);
        } else {
            $this->serveStaticFile($uri);
        }
    }

    private function servePublicPage()
    {
        if (file_exists(__DIR__ . '/public/index.html')) {
            require_once __DIR__ . '/public/index.html';
        } else {
            echo "DrxDion E-commerce Platform - Welcome!";
        }
    }
    private function handleApiRequest($uri, $method)
    {
        // CSRF Token endpoint
        if ($uri === '/api/csrf-token') {
            $pageController = $this->container->get(\DrxDion\Controllers\PageController::class);
            $pageController->csrfToken([]);
            return;
        }

        // Settings endpoints
        if ($uri === '/api/get-settings') {
            $this->handleGetSettings();
            return;
        }

        if ($uri === '/api/settings/update' && $method === 'POST') {
            $this->handleUpdateSettings();
            return;
        }

        // Orders API endpoints
        if (str_starts_with($uri, '/api/orders')) {
            $this->handleOrdersAPI($uri, $method);
            return;
        }

        header('Content-Type: application/json');
        http_response_code(404);
        echo json_encode(['error' => 'API endpoint not found', 'uri' => $uri, 'method' => $method]);
    }
    private function handleOrdersAPI($uri, $method)
    {
        header('Content-Type: application/json');

        // Parse URI and get order ID if present
        $parts = explode('/', trim($uri, '/'));
        $orderId = isset($parts[2]) ? $parts[2] : null;

        // GET /api/orders - List orders from database
        if ($uri === '/api/orders' && $method === 'GET') {
            try {
                // Get database connection
                $dbConfig = $this->container->resolve('db_config');
                $pdo = new PDO(
                    "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4",
                    $dbConfig['username'],
                    $dbConfig['password'],
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );

                // Get orders from database
                $stmt = $pdo->prepare("
                    SELECT 
                        id,
                        customer_name,
                        customer_email,
                        customer_phone,
                        items,
                        price,
                        total_amount,
                        shipping_cost,
                        payment_method,
                        payment_status,
                        status,
                        created_at,
                        updated_at
                    FROM orders 
                    ORDER BY created_at DESC
                ");
                $stmt->execute();
                $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Process orders for frontend
                $processedOrders = [];
                foreach ($orders as $order) {
                    $items = json_decode($order['items'] ?? '[]', true);
                    $productName = '';
                    $quantity = 1;

                    if (!empty($items) && is_array($items)) {
                        $firstItem = $items[0] ?? [];
                        $productName = $firstItem['name'] ?? 'Unknown Product';
                        $quantity = $firstItem['quantity'] ?? 1;
                    }

                    $processedOrders[] = [
                        'id' => $order['id'],
                        'customer_name' => $order['customer_name'],
                        'customer_email' => $order['customer_email'],
                        'customer_phone' => $order['customer_phone'],
                        'product_name' => $productName,
                        'quantity' => $quantity,
                        'price' => $order['price'],
                        'total_amount' => $order['total_amount'],
                        'shipping_cost' => $order['shipping_cost'],
                        'payment_method' => $order['payment_method'],
                        'payment_status' => $order['payment_status'],
                        'status' => $order['status'],
                        'created_at' => $order['created_at'],
                        'updated_at' => $order['updated_at']
                    ];
                }

                echo json_encode([
                    'status' => 'success',
                    'orders' => $processedOrders,
                    'total' => count($processedOrders),
                    'page' => 1,
                    'per_page' => 50
                ]);
                return;
            } catch (PDOException $e) {
                // Fallback to sample data if database fails
                $sampleOrders = [
                    [
                        'id' => '1',
                        'customer_name' => 'Ahmet Yılmaz',
                        'customer_email' => '<EMAIL>',
                        'customer_phone' => '+90 ************',
                        'product_name' => 'DrxDion Medikal Ürün A',
                        'quantity' => 2,
                        'price' => 150.00,
                        'total_amount' => 300.00,
                        'shipping_cost' => 25.00,
                        'payment_method' => 'Credit Card',
                        'status' => 'completed',
                        'created_at' => '2025-07-03 10:30:00',
                        'notes' => 'Hızlı teslimat istedi'
                    ]
                ];

                echo json_encode([
                    'status' => 'error',
                    'message' => 'Database connection failed: ' . $e->getMessage(),
                    'orders' => $sampleOrders,
                    'total' => count($sampleOrders),
                    'fallback' => true
                ]);
                return;
            }
        }

        // PUT /api/orders/{id}/status - Update order status
        if (preg_match('/^\/api\/orders\/(\w+)\/status$/', $uri, $matches) && $method === 'PUT') {
            $orderId = $matches[1];
            echo json_encode([
                'status' => 'success',
                'message' => "Order #{$orderId} status updated",
                'order_id' => $orderId
            ]);
            return;
        }

        // Single order operations
        if ($orderId) {
            if ($method === 'GET') {
                echo json_encode([
                    'status' => 'success',
                    'order' => [
                        'id' => $orderId,
                        'customer_name' => 'Sample Customer',
                        'status' => 'pending'
                    ]
                ]);
                return;
            }

            if ($method === 'PUT' || $method === 'PATCH') {
                echo json_encode([
                    'status' => 'success',
                    'message' => "Order #{$orderId} updated"
                ]);
                return;
            }

            if ($method === 'DELETE') {
                echo json_encode([
                    'status' => 'success',
                    'message' => "Order #{$orderId} deleted"
                ]);
                return;
            }
        }

        echo json_encode(['error' => 'Orders API endpoint not found']);
    }

    private function handleAdminRoot()
    {
        // Check if admin is logged in
        if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
            // Redirect to admin dashboard/orders
            header('Location: /admin/orders');
            exit;
        } else {
            // Redirect to login page
            header('Location: /admin/login');
            exit;
        }
    }

    private function handleAdminRequest($uri, $method)
    {
        // Handle admin login POST request
        if ($uri === '/admin/login' && $method === 'POST') {
            $this->handleAdminLogin();
            return;
        }

        // Handle admin logout request (both GET and POST)
        if ($uri === '/admin/logout' && ($method === 'POST' || $method === 'GET')) {
            $this->handleAdminLogout();
            return;
        }

        // Handle admin settings API
        if ($uri === '/api/get-settings') {
            $this->handleGetSettings();
            return;
        }

        if ($uri === '/api/settings/update' && $method === 'POST') {
            $this->handleUpdateSettings();
            return;
        }

        // Handle admin storage endpoints
        if (str_starts_with($uri, '/admin/storage/')) {
            $this->handleAdminStorageAPI($uri, $method);
            return;
        }

        // Handle admin orders API
        if (str_starts_with($uri, '/admin/orders') && $method !== 'GET') {
            $this->handleAdminOrdersAPI($uri, $method);
            return;
        }

        // Clean up the URI - remove trailing slashes
        $uri = rtrim($uri, '/');

        // If no extension, try .html first
        $originalFile = __DIR__ . '/public' . $uri;
        $htmlFile = $originalFile . '.html';

        $file = null;

        // Try different file possibilities
        if (file_exists($originalFile) && is_file($originalFile)) {
            $file = $originalFile;
        } elseif (file_exists($htmlFile) && is_file($htmlFile)) {
            $file = $htmlFile;
        } elseif (is_dir($originalFile)) {
            // If it's a directory, look for index.html
            $indexFile = $originalFile . '/index.html';
            if (file_exists($indexFile)) {
                $file = $indexFile;
            }
        }

        if ($file && is_file($file)) {
            // Determine content type
            $ext = pathinfo($file, PATHINFO_EXTENSION);
            switch ($ext) {
                case 'html':
                    header('Content-Type: text/html; charset=UTF-8');
                    break;
                case 'css':
                    header('Content-Type: text/css');
                    break;
                case 'js':
                    header('Content-Type: application/javascript');
                    break;
            }
            readfile($file);
        } else {
            header('HTTP/1.0 404 Not Found');
            echo "Admin page not found: " . $uri;
        }
    }

    private function handleAdminLogout()
    {
        // Check if it's an AJAX request or browser navigation
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        try {
            // Destroy session
            session_destroy();

            if ($isAjax || $_SERVER['REQUEST_METHOD'] === 'POST') {
                // AJAX or POST request - return JSON
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Logout successful',
                    'redirect' => '/admin/login'
                ]);
            } else {
                // GET request - redirect directly
                header('Location: /admin/login');
                exit;
            }
        } catch (Exception $e) {
            if ($isAjax || $_SERVER['REQUEST_METHOD'] === 'POST') {
                header('Content-Type: application/json');
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Server error: ' . $e->getMessage()
                ]);
            } else {
                header('Location: /admin/login?error=logout_failed');
                exit;
            }
        }
    }

    private function handleAdminLogin()
    {
        header('Content-Type: application/json');

        try {
            // Get POST data
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Invalid JSON data']);
                return;
            }

            $username = $data['username'] ?? '';
            $password = $data['password'] ?? '';

            // Simple hardcoded admin credentials
            if ($username === 'admin' && $password === 'admin123') {
                // Set session
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_user'] = $username;
                $_SESSION['login_time'] = time();

                echo json_encode([
                    'success' => true,
                    'message' => 'Login successful',
                    'redirect' => '/admin/orders'
                ]);
            } else {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid username or password'
                ]);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Server error: ' . $e->getMessage()
            ]);
        }
    }

    private function handleGetSettings()
    {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'data' => [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'notification_email' => '<EMAIL>'
            ]
        ]);
    }

    private function handleUpdateSettings()
    {
        try {
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            $adminController = $this->container->get(\DrxDion\Controllers\AdminController::class);
            $adminController->updateSettings([
                'body' => $data,
                'headers' => getallheaders()
            ]);
        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to update settings: ' . $e->getMessage()
            ]);
        }
    }

    private function handleAdminStorageAPI($uri, $method)
    {
        try {
            $adminController = $this->container->get(\DrxDion\Controllers\AdminController::class);

            if ($uri === '/admin/storage/status') {
                $adminController->getStorageStatus([]);
                return;
            }

            if ($uri === '/admin/storage/sync' && $method === 'POST') {
                $input = file_get_contents('php://input');
                $data = json_decode($input, true);

                $adminController->syncStorage([
                    'body' => $data,
                    'headers' => getallheaders()
                ]);
                return;
            }

            header('Content-Type: application/json');
            http_response_code(404);
            echo json_encode(['error' => 'Storage endpoint not found']);
        } catch (\Exception $e) {
            header('Content-Type: application/json');
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Storage API error: ' . $e->getMessage()
            ]);
        }
    }

    private function handleAdminOrdersAPI($uri, $method)
    {
        header('Content-Type: application/json');

        // Basic orders API responses
        if ($method === 'POST' && $uri === '/admin/orders') {
            echo json_encode(['status' => 'success', 'message' => 'Order created']);
            return;
        }

        if ($method === 'PUT' || $method === 'PATCH') {
            echo json_encode(['status' => 'success', 'message' => 'Order updated']);
            return;
        }

        if ($method === 'DELETE') {
            echo json_encode(['status' => 'success', 'message' => 'Order deleted']);
            return;
        }

        echo json_encode(['error' => 'Orders endpoint not found']);
    }

    private function serveStaticFile($uri)
    {
        $file = __DIR__ . '/public' . $uri;
        if (file_exists($file) && is_file($file)) {
            $ext = pathinfo($file, PATHINFO_EXTENSION);
            switch ($ext) {
                case 'css':
                    header('Content-Type: text/css');
                    break;
                case 'js':
                    header('Content-Type: application/javascript');
                    break;
                case 'png':
                    header('Content-Type: image/png');
                    break;
                case 'jpg':
                case 'jpeg':
                    header('Content-Type: image/jpeg');
                    break;
                case 'webp':
                    header('Content-Type: image/webp');
                    break;
                case 'ico':
                    header('Content-Type: image/x-icon');
                    break;
            }
            readfile($file);
        } else {
            header('HTTP/1.0 404 Not Found');
            echo "File not found: " . $uri;
        }
    }
}

$router = new SimpleRouter($container);
