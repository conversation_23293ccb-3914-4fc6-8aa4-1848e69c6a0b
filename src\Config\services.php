<?php

declare(strict_types=1);

use DrxDion\Config\Container;
use DrxDion\Interfaces\OrderRepositoryInterface;
use DrxDion\Interfaces\AdminRepositoryInterface;
use DrxDion\Interfaces\PaymentGatewayInterface;
use DrxDion\Interfaces\EmailServiceInterface;
use DrxDion\Interfaces\SecurityServiceInterface;
use DrxDion\Infrastructure\Persistence\SQLite\OrderRepository;
use DrxDion\Infrastructure\Persistence\SQLite\AdminRepository;
use DrxDion\Infrastructure\Persistence\SQLite\DatabaseConnection;
use DrxDion\Database\MySQL\MySQLConnection;
use DrxDion\Database\MySQL\MySQLOrderRepository;
use DrxDion\Database\MySQL\MySQLAdminRepository;
use DrxDion\Database\MySQL\MySQLSettingsRepository;
use DrxDion\Database\JSON\JsonOrderRepository;
use DrxDion\Database\JSON\JsonAdminRepository;
use DrxDion\Database\Composite\CompositeOrderRepository;
use DrxDion\Database\Smart\SmartOrderRepository;
use DrxDion\Services\StorageService;
use DrxDion\Controllers\AdminController;
use DrxDion\Infrastructure\Payment\IyzicoPaymentGateway;
use DrxDion\Infrastructure\Email\PHPMailerService;
use DrxDion\Infrastructure\Security\SecurityService;
use DrxDion\Application\Services\OrderService;
use DrxDion\Application\Services\AdminService;
use DrxDion\Application\Services\PaymentService;
use DrxDion\Presentation\Controllers\OrderController;
use DrxDion\Presentation\Controllers\PaymentController;
use DrxDion\Presentation\Controllers\PageController;

/**
 * Service Registration
 * Register all services in the DI container
 */
function registerServices(Container $container): void
{
    // Always ensure JSON repository is available as fallback
    $container->singleton(JsonOrderRepository::class);
    $container->singleton(JsonAdminRepository::class);

    // Check database configuration
    $dbType = 'mysql'; // Prefer MySQL
    $useMySQL = extension_loaded('pdo_mysql');
    $useSQLite = false; // Disable SQLite for now

    // Try to set up MySQL if available
    $mysqlRepository = null;
    if ($useMySQL) {
        try {
            // MySQL Database
            $container->singleton(MySQLConnection::class, function () {
                return new MySQLConnection(
                    \DrxDion\Infrastructure\Config\Environment::get('database.mysql.host'),
                    \DrxDion\Infrastructure\Config\Environment::get('database.mysql.database'),
                    \DrxDion\Infrastructure\Config\Environment::get('database.mysql.username'),
                    \DrxDion\Infrastructure\Config\Environment::get('database.mysql.password'),
                    \DrxDion\Infrastructure\Config\Environment::get('database.mysql.port')
                );
            });

            // MySQL Repositories
            $container->singleton(MySQLOrderRepository::class);
            $container->singleton(MySQLSettingsRepository::class);

            // Storage Service
            $container->singleton(StorageService::class, function (Container $container) {
                return new StorageService(
                    $container->get(MySQLConnection::class),
                    $container->get(JsonOrderRepository::class),
                    $container->get(MySQLOrderRepository::class)
                );
            });

            // Smart Order Repository with MySQL + JSON fallback
            $container->bind(OrderRepositoryInterface::class, function (Container $container) {
                try {
                    $mysqlRepo = $container->get(MySQLOrderRepository::class);
                } catch (\Exception $e) {
                    error_log("MySQL repository initialization failed: " . $e->getMessage());
                    $mysqlRepo = null;
                }

                return new SmartOrderRepository(
                    $mysqlRepo,
                    $container->get(JsonOrderRepository::class)
                );
            });

            $container->bind(AdminRepositoryInterface::class, MySQLAdminRepository::class);
        } catch (\Exception $e) {
            error_log("MySQL setup failed, using JSON fallback: " . $e->getMessage());
            // Fall through to JSON-only setup
            $useMySQL = false;
        }
    }

    if (!$useMySQL) {
        if ($useSQLite) {
            // SQLite Database
            $container->singleton(DatabaseConnection::class, function () {
                return new DatabaseConnection(\DrxDion\Infrastructure\Config\Environment::get('database.sqlite.path'));
            });

            // SQLite Repositories
            $container->bind(OrderRepositoryInterface::class, OrderRepository::class);
            $container->bind(AdminRepositoryInterface::class, AdminRepository::class);
        } else {
            // JSON File Repositories (fallback) - Smart repo with JSON only
            $container->bind(OrderRepositoryInterface::class, function (Container $container) {
                return new SmartOrderRepository(
                    null, // No MySQL
                    $container->get(JsonOrderRepository::class)
                );
            });
            $container->bind(AdminRepositoryInterface::class, JsonAdminRepository::class);
        }
    }

    // Infrastructure Services
    $container->bind(PaymentGatewayInterface::class, IyzicoPaymentGateway::class);
    $container->bind(EmailServiceInterface::class, PHPMailerService::class);
    $container->bind(SecurityServiceInterface::class, SecurityService::class);

    // Application Services
    $container->bind(OrderService::class);
    $container->bind(AdminService::class);
    $container->bind(PaymentService::class);

    // Controllers
    $container->bind(OrderController::class);
    $container->bind(AdminController::class);
    $container->bind(PaymentController::class);
    $container->bind(PageController::class);
}
