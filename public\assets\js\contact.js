document.addEventListener("DOMContentLoaded", function () {
	const contactForm = document.getElementById("contactForm");
	if (contactForm) {
		contactForm.addEventListener("submit", async function (e) {
			e.preventDefault();

			const formData = {
				name: document.getElementById("name").value,
				email: document.getElementById("email").value,
				subject: document.getElementById("subject").value,
				message: document.getElementById("message").value,
			};			try {
				// Get CSRF token
				function getCSRFToken() {
					// Try meta tag first
					const metaTag = document.querySelector('meta[name="csrf-token"]');
					if (metaTag && metaTag.getAttribute('content')) {
						return metaTag.getAttribute('content');
					}
					
					// Fallback to cookie
					const value = `; ${document.cookie}`;
					const parts = value.split(`; csrf-token=`);
					if (parts.length === 2) {
						return parts.pop().split(";").shift();
					}
					
					return null;
				}

				const csrfToken = getCSRFToken();
				if (!csrfToken) {
					throw new Error("CSRF token not found. Please refresh the page and try again.");
				}

				const response = await fetch("/api/contact", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"X-CSRF-Token": csrfToken,
					},
					body: JSON.stringify(formData),
				});

				const result = await response.json();

				if (response.ok) {
					alert("Message sent successfully!");
					contactForm.reset();
				} else {
					throw new Error(result.error || "Failed to send message");
				}
			} catch (error) {
				alert("Error sending message: " + error.message);
			}
		});
	}
});
