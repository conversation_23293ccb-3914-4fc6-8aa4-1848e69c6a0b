{"name": "drxdion/ecommerce-platform", "version": "1.3.0", "description": "DrxDion E-commerce Platform - PHP 8.0 Clean Architecture Version", "type": "project", "require": {"php": "^8.0"}, "autoload": {"psr-4": {"App\\": "app/", "DrxDion\\": "src/"}}, "scripts": {"init-db": "php scripts/init-database.php", "clean-orders": "php scripts/clean-orders.php", "check-env": "php scripts/check-env.php", "generate-secrets": "php scripts/generate-secrets.php", "serve": "php -S localhost:8000 -t . index.php"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}}