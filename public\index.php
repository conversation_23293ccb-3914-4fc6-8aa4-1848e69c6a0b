<?php

declare(strict_types=1);

/**
 * Laravel-style Entry Point
 * DrxDion E-commerce Platform
 */

// Disable error reporting for production
error_reporting(0);
ini_set('display_errors', '0');

// Bootstrap the application
$containerClass = require_once __DIR__ . '/../bootstrap/app.php';

// Simple Router Class
class Router
{
    private array $routes = [];
    private $container;

    public function __construct($containerClass)
    {
        $this->container = $containerClass;
        $this->loadRoutes();
    }

    private function loadRoutes(): void
    {
        // Load web routes
        $webRoutes = require BASE_PATH . '/routes/web.php';
        foreach ($webRoutes as $uri => $route) {
            $this->routes[$uri] = $route;
        }

        // Load API routes
        $apiRoutes = require BASE_PATH . '/routes/api.php';
        foreach ($apiRoutes as $uri => $route) {
            $this->routes[$uri] = $route;
        }
    }

    public function dispatch(): void
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

        // Remove query string
        $uri = strtok($uri, '?');

        // Clean up URI - remove folder name if present
        if (str_starts_with($uri, '/drxdion/')) {
            $uri = substr($uri, 8); // Remove '/drxdion'
        }
        if (empty($uri)) {
            $uri = '/';
        }

        // Normalize URI: remove trailing slash except for root
        if ($uri !== '/' && str_ends_with($uri, '/')) {
            $uri = rtrim($uri, '/');
        }

        // Special handling for admin routes (but not static files)
        if (str_starts_with($uri, '/admin') && !$this->isStaticFile($uri)) {
            $this->handleAdminRoute($uri, $method);
            return;
        }

        // Try exact match first
        foreach ($this->routes as $routeUri => $route) {
            $normalizedRouteUri = ($routeUri !== '/' && str_ends_with($routeUri, '/')) ? rtrim($routeUri, '/') : $routeUri;
            if ($uri === $normalizedRouteUri && $this->methodMatches($method, $route[0])) {
                $this->callController($route[1]);
                return;
            }
        }

        // Try pattern matching for dynamic routes
        foreach ($this->routes as $pattern => $route) {
            $normalizedPattern = ($pattern !== '/' && str_ends_with($pattern, '/')) ? rtrim($pattern, '/') : $pattern;
            if ($this->patternMatches($uri, $normalizedPattern) && $this->methodMatches($method, $route[0])) {
                $this->callController($route[1]);
                return;
            }
        }

        // Handle static files if no route matches
        if ($this->isStaticFile($uri)) {
            $this->serveStaticFile($uri);
            return;
        }

        // 404 if nothing matches
        http_response_code(404);
        echo "404 - Page not found: " . htmlspecialchars($uri);
    }

    private function methodMatches(string $method, string $allowedMethods): bool
    {
        $methods = explode('|', $allowedMethods);
        return in_array($method, $methods) || in_array('*', $methods);
    }

    private function patternMatches(string $uri, string $pattern): bool
    {
        $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $pattern);
        $result = preg_match('#^' . $pattern . '$#', $uri);
        return $result === 1;
    }

    private function callController(string $controllerAction): void
    {
        [$controller, $method] = explode('@', $controllerAction);

        try {
            // Use container to resolve controller with dependencies
            $controllerInstance = $this->container::resolve($controller);
            $controllerInstance->$method();
        } catch (\Exception $e) {
            http_response_code(500);
            echo "Controller error: " . $e->getMessage() . "<br>";
            echo "Stack trace: " . $e->getTraceAsString();
        }
    }

    private function isStaticFile(string $uri): bool
    {
        $filePath = BASE_PATH . '/public' . $uri;
        return file_exists($filePath) && is_file($filePath);
    }

    private function serveStaticFile(string $uri): void
    {
        $filePath = BASE_PATH . '/public' . $uri;

        if (!file_exists($filePath) || !is_file($filePath)) {
            http_response_code(404);
            echo "File not found";
            return;
        }

        // Determine content type
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        $contentType = match ($ext) {
            'html' => 'text/html; charset=UTF-8',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'png' => 'image/png',
            'jpg', 'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            default => 'application/octet-stream'
        };

        header('Content-Type: ' . $contentType);
        readfile($filePath);
    }

    private function handleAdminRoute(string $uri, string $method): void
    {
        // Start session if not started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Admin route mapping
        $adminRoutes = [
            '/admin' => 'adminRoot',
            '/admin/' => 'adminRoot',
            '/admin/login' => 'adminLogin',
            '/admin/orders' => 'adminOrders',
            '/admin/settings' => 'adminSettings',
            '/admin/storage' => 'adminStorage',
        ];

        if (isset($adminRoutes[$uri]) && $method === 'GET') {
            try {
                $controllerInstance = $this->container::resolve('App\Http\Controllers\PageController');
                $methodName = $adminRoutes[$uri];
                $controllerInstance->$methodName();
            } catch (\Exception $e) {
                http_response_code(500);
                echo "Admin route error: " . $e->getMessage();
            }
            return;
        }

        // Handle admin API routes (POST requests)
        if ($uri === '/admin/login' && $method === 'POST') {
            try {
                $controllerInstance = $this->container::resolve('App\Http\Controllers\AdminController');
                $controllerInstance->login();
            } catch (\Exception $e) {
                http_response_code(500);
                echo "Admin API error: " . $e->getMessage();
            }
            return;
        }

        if ($uri === '/admin/logout' && ($method === 'POST' || $method === 'GET')) {
            try {
                $controllerInstance = $this->container::resolve('App\Http\Controllers\AdminController');
                $controllerInstance->logout();
            } catch (\Exception $e) {
                http_response_code(500);
                echo "Admin API error: " . $e->getMessage();
            }
            return;
        }

        // Handle admin storage API routes
        if ($uri === '/admin/storage/status' && $method === 'GET') {
            try {
                $controllerInstance = $this->container::resolve('App\Http\Controllers\AdminController');
                $result = $controllerInstance->getStorageStatus();
                header('Content-Type: application/json');
                echo json_encode($result);
            } catch (\Exception $e) {
                http_response_code(500);
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            return;
        }

        if ($uri === '/admin/storage/sync' && $method === 'POST') {
            try {
                $input = file_get_contents('php://input');
                $data = json_decode($input, true) ?? [];

                $controllerInstance = $this->container::resolve('App\Http\Controllers\AdminController');
                $result = $controllerInstance->syncStorage($data);
                header('Content-Type: application/json');
                echo json_encode($result);
            } catch (\Exception $e) {
                http_response_code(500);
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            return;
        }

        // If no admin route matches, return 404
        http_response_code(404);
        echo "Admin page not found: " . htmlspecialchars($uri);
    }
}

// Create and dispatch the router
$router = new Router($containerClass);
$router->dispatch();
