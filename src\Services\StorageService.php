<?php

declare(strict_types=1);

namespace DrxDion\Services;

use DrxDion\Interfaces\StorageRepositoryInterface;
use DrxDion\Interfaces\OrderRepositoryInterface;
use DrxDion\Database\MySQL\MySQLConnection;
use DrxDion\Database\JSON\JsonOrderRepository;
use DrxDion\Database\MySQL\MySQLOrderRepository;

/**
 * Storage Service
 * Manages storage monitoring, synchronization and health metrics
 */
final class StorageService implements StorageRepositoryInterface
{
    public function __construct(
        private readonly MySQLConnection $mysqlConnection,
        private readonly JsonOrderRepository $jsonRepository,
        private readonly MySQLOrderRepository $mysqlRepository
    ) {}

    public function getStatus(): array
    {
        $mysqlStatus = $this->getMySQLStatus();
        $jsonStatus = $this->getJSONStatus();

        return [
            'mysql' => $mysqlStatus,
            'json' => $jsonStatus,
            'last_sync' => $this->getLastSyncTime(),
            'overall_health' => $mysqlStatus['connected'] && $jsonStatus['connected'] ? 'healthy' : 'warning'
        ];
    }

    public function getSyncStats(): array
    {
        try {
            $mysqlCount = $this->mysqlRepository->count();
            $jsonCount = $this->jsonRepository->count();

            return [
                'mysql_records' => $mysqlCount,
                'json_records' => $jsonCount,
                'sync_needed' => $mysqlCount !== $jsonCount,
                'difference' => abs($mysqlCount - $jsonCount)
            ];
        } catch (\Exception $e) {
            return [
                'mysql_records' => 0,
                'json_records' => 0,
                'sync_needed' => false,
                'difference' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    public function sync(bool $forceReplace = false): array
    {
        try {
            $startTime = microtime(true);
            $syncedRecords = 0;
            $errors = [];

            // Get all orders from MySQL (primary source)
            $mysqlOrders = $this->mysqlRepository->findAll();

            if ($forceReplace) {
                // Clear JSON storage first
                $this->jsonRepository->clear();
            }

            // Sync each order to JSON
            foreach ($mysqlOrders as $order) {
                try {
                    if ($forceReplace || !$this->jsonRepository->findById($order->getId())) {
                        if ($this->jsonRepository->save($order)) {
                            $syncedRecords++;
                        }
                    }
                } catch (\Exception $e) {
                    $errors[] = "Failed to sync order {$order->getId()}: " . $e->getMessage();
                }
            }

            // Update last sync timestamp
            $this->updateLastSyncTime();

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 3);

            return [
                'success' => true,
                'synced_records' => $syncedRecords,
                'total_records' => count($mysqlOrders),
                'duration_seconds' => $duration,
                'errors' => $errors,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'synced_records' => 0,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }

    public function getHealthMetrics(): array
    {
        $mysqlHealth = $this->getMySQLHealth();
        $jsonHealth = $this->getJSONHealth();
        $syncStats = $this->getSyncStats();

        return [
            'mysql' => $mysqlHealth,
            'json' => $jsonHealth,
            'sync' => [
                'status' => $syncStats['sync_needed'] ? 'out_of_sync' : 'synchronized',
                'last_sync' => $this->getLastSyncTime(),
                'difference' => $syncStats['difference']
            ],
            'overall_score' => $this->calculateHealthScore($mysqlHealth, $jsonHealth, $syncStats)
        ];
    }

    public function getLastSyncTime(): ?string
    {
        $syncFile = __DIR__ . '/../../data/last_sync.txt';
        if (file_exists($syncFile)) {
            return trim(file_get_contents($syncFile));
        }
        return null;
    }

    private function getMySQLStatus(): array
    {
        try {
            $pdo = $this->mysqlConnection->getConnection();
            $stmt = $pdo->query('SELECT COUNT(*) as count FROM orders');
            $count = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] ?? 0;

            return [
                'connected' => true,
                'records' => (int) $count,
                'type' => 'mysql',
                'status' => 'healthy'
            ];
        } catch (\Exception $e) {
            return [
                'connected' => false,
                'records' => 0,
                'type' => 'mysql',
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getJSONStatus(): array
    {
        try {
            $count = $this->jsonRepository->count();

            return [
                'connected' => true,
                'records' => $count,
                'type' => 'json',
                'status' => 'healthy'
            ];
        } catch (\Exception $e) {
            return [
                'connected' => false,
                'records' => 0,
                'type' => 'json',
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getMySQLHealth(): array
    {
        try {
            $pdo = $this->mysqlConnection->getConnection();

            // Check connection
            $pdo->query('SELECT 1');

            // Get table status
            $stmt = $pdo->query('SHOW TABLE STATUS LIKE "orders"');
            $tableInfo = $stmt->fetch(\PDO::FETCH_ASSOC);

            return [
                'connection' => 'healthy',
                'table_size' => $tableInfo['Data_length'] ?? 0,
                'row_count' => $tableInfo['Rows'] ?? 0,
                'engine' => $tableInfo['Engine'] ?? 'unknown'
            ];
        } catch (\Exception $e) {
            return [
                'connection' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getJSONHealth(): array
    {
        try {
            $dataDir = __DIR__ . '/../../data';
            $ordersFile = $dataDir . '/orders.json';

            $health = [
                'file_exists' => file_exists($ordersFile),
                'readable' => is_readable($ordersFile),
                'writable' => is_writable(dirname($ordersFile))
            ];

            if ($health['file_exists']) {
                $health['file_size'] = filesize($ordersFile);
                $health['last_modified'] = date('Y-m-d H:i:s', filemtime($ordersFile));
            }

            return $health;
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }

    private function calculateHealthScore(array $mysqlHealth, array $jsonHealth, array $syncStats): int
    {
        $score = 100;

        // Deduct points for MySQL issues
        if (isset($mysqlHealth['connection']) && $mysqlHealth['connection'] !== 'healthy') {
            $score -= 40;
        }

        // Deduct points for JSON issues
        if (!($jsonHealth['file_exists'] ?? false)) {
            $score -= 20;
        }
        if (!($jsonHealth['writable'] ?? false)) {
            $score -= 10;
        }

        // Deduct points for sync issues
        if ($syncStats['sync_needed'] ?? false) {
            $score -= 15;
        }

        return max(0, $score);
    }

    private function updateLastSyncTime(): void
    {
        $dataDir = __DIR__ . '/../../data';
        if (!is_dir($dataDir)) {
            mkdir($dataDir, 0755, true);
        }

        file_put_contents(
            $dataDir . '/last_sync.txt',
            date('Y-m-d H:i:s')
        );
    }
}
