<?php

declare(strict_types=1);

namespace DrxDion\Models;

use DateTimeImmutable;

/**
 * Admin User Entity
 * Represents an administrative user
 */
final class AdminUser
{
    public function __construct(
        private int $id,
        private string $username,
        private string $passwordHash,
        private string $email,
        private DateTimeImmutable $createdAt,
        private DateTimeImmutable $updatedAt
    ) {}

    public static function create(
        int $id,
        string $username,
        string $passwordHash,
        string $email
    ): self {
        $now = new DateTimeImmutable();

        return new self(
            $id,
            $username,
            $passwordHash,
            $email,
            $now,
            $now
        );
    }

    public function updatePassword(string $newPasswordHash): self
    {
        return new self(
            $this->id,
            $this->username,
            $newPasswordHash,
            $this->email,
            $this->createdAt,
            new DateTimeImmutable()
        );
    }

    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->passwordHash);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function getPasswordHash(): string
    {
        return $this->passwordHash;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'username' => $this->username,
            'email' => $this->email,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s'),
        ];
    }
}
