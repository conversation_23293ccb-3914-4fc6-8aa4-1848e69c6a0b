<?php

declare(strict_types=1);

namespace App\Utils;

/**
 * Environment File Manager
 * Handles reading and writing .env file
 */
class EnvManager
{
    private static string $envPath;

    public static function init(): void
    {
        self::$envPath = dirname(__DIR__, 2) . '/.env';
    }

    /**
     * Get environment variable value
     */
    public static function get(string $key, ?string $default = null): ?string
    {
        self::init();
        
        if (!file_exists(self::$envPath)) {
            return $default;
        }

        $content = file_get_contents(self::$envPath);
        if (!$content) {
            return $default;
        }

        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip comments and empty lines
            if (empty($line) || str_starts_with($line, '#')) {
                continue;
            }

            // Parse key=value
            if (str_contains($line, '=')) {
                [$envKey, $envValue] = explode('=', $line, 2);
                $envKey = trim($envKey);
                $envValue = trim($envValue);
                
                if ($envKey === $key) {
                    return $envValue;
                }
            }
        }

        return $default;
    }

    /**
     * Set environment variable value
     */
    public static function set(string $key, string $value): bool
    {
        self::init();
        
        if (!file_exists(self::$envPath)) {
            // Create new .env file
            $content = "$key=$value\n";
            return file_put_contents(self::$envPath, $content) !== false;
        }

        $content = file_get_contents(self::$envPath);
        if ($content === false) {
            return false;
        }

        $lines = explode("\n", $content);
        $updated = false;
        
        // Update existing key or add new one
        for ($i = 0; $i < count($lines); $i++) {
            $line = trim($lines[$i]);
            
            if (empty($line) || str_starts_with($line, '#')) {
                continue;
            }

            if (str_contains($line, '=')) {
                [$envKey] = explode('=', $line, 2);
                $envKey = trim($envKey);
                
                if ($envKey === $key) {
                    $lines[$i] = "$key=$value";
                    $updated = true;
                    break;
                }
            }
        }

        // If key not found, add it at the end
        if (!$updated) {
            $lines[] = "$key=$value";
        }

        $newContent = implode("\n", $lines);
        return file_put_contents(self::$envPath, $newContent) !== false;
    }

    /**
     * Set multiple environment variables
     */
    public static function setMultiple(array $variables): bool
    {
        foreach ($variables as $key => $value) {
            if (!self::set($key, $value)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Get all environment variables as array
     */
    public static function getAll(): array
    {
        self::init();
        
        if (!file_exists(self::$envPath)) {
            return [];
        }

        $content = file_get_contents(self::$envPath);
        if (!$content) {
            return [];
        }

        $variables = [];
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip comments and empty lines
            if (empty($line) || str_starts_with($line, '#')) {
                continue;
            }

            // Parse key=value
            if (str_contains($line, '=')) {
                [$key, $value] = explode('=', $line, 2);
                $variables[trim($key)] = trim($value);
            }
        }

        return $variables;
    }
}
