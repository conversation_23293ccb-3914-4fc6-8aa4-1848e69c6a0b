<?php

declare(strict_types=1);

/**
 * Web Routes
 * DrxDion E-commerce Platform
 */

return [
    // Debug route
    '/debug' => ['GET', 'App\Http\Controllers\PageController@debug'],

    // Public routes
    '/' => ['GET', 'App\Http\Controllers\PageController@index'],
    '/index.php' => ['GET', 'App\Http\Controllers\PageController@index'],

    // Admin routes
    '/admin' => ['GET', 'App\Http\Controllers\PageController@adminOrders'],
    '/admin/' => ['GET', 'App\Http\Controllers\PageController@adminOrders'],
    '/admin/login' => ['GET', 'App\Http\Controllers\PageController@adminLogin'],
    '/admin/orders' => ['GET', 'App\Http\Controllers\PageController@adminOrders'],
    '/admin/settings' => ['GET', 'App\Http\Controllers\PageController@adminSettings'],
];
