<?php

declare(strict_types=1);

/**
 * Environment Check Script
 * PHP equivalent of npm run check-env
 */

echo "🔍 DrxDion Environment Configuration Check\n\n";

// Load environment
require_once __DIR__ . '/../src/Infrastructure/Config/Environment.php';
\DrxDion\Infrastructure\Config\Environment::load(__DIR__ . '/../.env');

$requiredVars = [
    'EMAIL_USER',
    'EMAIL_PASS',
    'EMAIL_TO',
    'SESSION_SECRET',
    'IYZIPAY_API_KEY',
    'IYZIPAY_SECRET_KEY',
    'IYZIPAY_URI',
    'BASE_URL',
    'RECAPTCHA_SITE_KEY',
    'RECAPTCHA_SECRET_KEY',
    'ORDER_NOTIFICATION_TO',
    'IYZICO_WEBHOOK_SECRET',
];

$warnings = [];
$errors = [];

echo "📋 Checking required environment variables...\n\n";

foreach ($requiredVars as $varName) {
    $value = \DrxDion\Infrastructure\Config\Environment::env($varName);

    if (empty($value)) {
        $errors[] = "❌ {$varName} is not set";
    } elseif (str_contains($value, 'your-') || str_contains($value, 'change-this')) {
        $warnings[] = "⚠️  {$varName} appears to use default/placeholder value";
    } else {
        echo "✅ {$varName} is configured\n";
    }
}

echo "\n🔒 Security checks...\n\n";

// Check session secret strength
$sessionSecret = \DrxDion\Infrastructure\Config\Environment::env('SESSION_SECRET');
if (!empty($sessionSecret) && strlen($sessionSecret) < 32) {
    $warnings[] = "⚠️  SESSION_SECRET should be at least 32 characters long";
} elseif (!empty($sessionSecret) && strlen($sessionSecret) >= 32) {
    echo "✅ SESSION_SECRET has adequate length\n";
}

// Check database path
$dbPath = \DrxDion\Infrastructure\Config\Environment::get('database.path');
if (file_exists($dbPath)) {
    echo "✅ Database file exists\n";
} else {
    echo "⚠️  Database file doesn't exist (will be created on first run)\n";
}

// Display warnings and errors
if (!empty($warnings)) {
    echo "\n⚠️  WARNINGS:\n";
    foreach ($warnings as $warning) {
        echo $warning . "\n";
    }
}

if (!empty($errors)) {
    echo "\n❌ ERRORS:\n";
    foreach ($errors as $error) {
        echo $error . "\n";
    }
    echo "\n❌ Configuration incomplete. Please check your .env file.\n";
    exit(1);
}

echo "\n✅ Environment configuration looks good!\n";

if (!empty($warnings)) {
    echo "\n⚠️  Please address the warnings above for optimal security.\n";
    exit(1);
}

exit(0);
