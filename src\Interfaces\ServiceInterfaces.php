<?php

declare(strict_types=1);

namespace DrxDion\Interfaces;

/**
 * Payment Gateway Interface
 * Contract for payment processing services
 */
interface PaymentGatewayInterface
{
    /**
     * Initialize payment
     */
    public function initializePayment(array $orderData): array;

    /**
     * Verify payment callback
     */
    public function verifyCallback(array $callbackData): array;

    /**
     * Process webhook
     */
    public function processWebhook(string $rawData): array;

    /**
     * Get payment status
     */
    public function getPaymentStatus(string $paymentId): string;
}

/**
 * Email Service Interface
 * Contract for email services
 */
interface EmailServiceInterface
{
    /**
     * Send order notification email
     */
    public function sendOrderNotification(array $orderData): bool;

    /**
     * Send contact form email
     */
    public function sendContactForm(array $contactData): bool;

    /**
     * Test email configuration
     */
    public function testConfiguration(): bool;
}

/**
 * Security Service Interface
 * Contract for security services
 */
interface SecurityServiceInterface
{
    /**
     * Generate CSRF token
     */
    public function generateCsrfToken(): string;

    /**
     * Verify CSRF token
     */
    public function verifyCsrfToken(string $token): bool;

    /**
     * Hash password
     */
    public function hashPassword(string $password): string;

    /**
     * Verify password
     */
    public function verifyPassword(string $password, string $hash): bool;

    /**
     * Generate secure random string
     */
    public function generateSecureRandom(int $length = 32): string;

    /**
     * Verify reCAPTCHA
     */
    public function verifyRecaptcha(string $response, string $remoteIp): bool;

    /**
     * Check rate limiting
     */
    public function checkRateLimit(string $key, int $maxAttempts, int $windowSeconds): bool;
}
