<?php

declare(strict_types=1);

echo "=== ADMIN ROUTE TEST ===\n";

// Test admin route specifically
$uri = '/admin';
$method = 'GET';

echo "URI: $uri\n";
echo "Method: $method\n";

// Load routes
$webRoutes = require __DIR__ . '/../routes/web.php';

echo "Testing route: $uri\n";

if (isset($webRoutes[$uri])) {
    echo "MATCH FOUND!\n";
    $route = $webRoutes[$uri];
    echo "Method: {$route[0]}\n";
    echo "Controller: {$route[1]}\n";
    
    // Test controller loading
    require_once __DIR__ . '/../bootstrap/app.php';
    
    [$controller, $method] = explode('@', $route[1]);
    
    echo "Controller class: $controller\n";
    echo "Method: $method\n";
    
    if (class_exists($controller)) {
        echo "Class exists: YES\n";
        try {
            $instance = Container::resolve($controller);
            echo "Container resolved: YES\n";
            
            if (method_exists($instance, $method)) {
                echo "Method exists: YES\n";
                echo "Ready to call: $controller@$method\n";
            } else {
                echo "Method exists: NO\n";
            }
        } catch (Exception $e) {
            echo "Container error: " . $e->getMessage() . "\n";
        }
    } else {
        echo "Class exists: NO\n";
    }
} else {
    echo "NO MATCH\n";
}

?>
