<?php

declare(strict_types=1);

namespace DrxDion\Database\MySQL;

use DrxDion\Interfaces\AdminRepositoryInterface;
use DrxDion\Models\AdminUser;
use DateTimeImmutable;
use PDOException;
use RuntimeException;

/**
 * MySQL Admin Repository Implementation
 */
final class MySQLAdminRepository implements AdminRepositoryInterface
{
    public function __construct(
        private MySQLConnection $database
    ) {}

    public function save(AdminUser $admin): void
    {
        try {
            $sql = "
                INSERT INTO admin_users (
                    id, username, password_hash, email, created_at, updated_at
                ) VALUES (
                    :id, :username, :password_hash, :email, :created_at, :updated_at
                )
                ON DUPLICATE KEY UPDATE
                    username = VALUES(username),
                    password_hash = VALUES(password_hash),
                    email = VALUES(email),
                    updated_at = VALUES(updated_at)
            ";

            $stmt = $this->database->getConnection()->prepare($sql);

            $stmt->execute([
                ':id' => $admin->getId(),
                ':username' => $admin->getUsername(),
                ':password_hash' => $admin->getPasswordHash(),
                ':email' => $admin->getEmail(),
                ':created_at' => $admin->getCreatedAt()->format('Y-m-d H:i:s'),
                ':updated_at' => $admin->getUpdatedAt()->format('Y-m-d H:i:s')
            ]);
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to save admin user: " . $e->getMessage(), 0, $e);
        }
    }

    public function findByUsername(string $username): ?AdminUser
    {
        try {
            $sql = "SELECT * FROM admin_users WHERE username = :username LIMIT 1";
            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute([':username' => $username]);

            $row = $stmt->fetch();

            return $row ? $this->rowToAdmin($row) : null;
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to find admin user: " . $e->getMessage(), 0, $e);
        }
    }

    public function findById(int $id): ?AdminUser
    {
        try {
            $sql = "SELECT * FROM admin_users WHERE id = :id LIMIT 1";
            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute([':id' => $id]);

            $row = $stmt->fetch();

            return $row ? $this->rowToAdmin($row) : null;
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to find admin user by ID: " . $e->getMessage(), 0, $e);
        }
    }

    public function hasAnyAdmins(): bool
    {
        try {
            $sql = "SELECT COUNT(*) FROM admin_users";
            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute();

            return (int) $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to check admin users: " . $e->getMessage(), 0, $e);
        }
    }

    public function createDefaultAdmin(string $username, string $password, string $email): AdminUser
    {
        try {
            // Get next ID
            $nextId = $this->getNextId();

            $admin = AdminUser::create($nextId, $username, $password, $email);
            $this->save($admin);

            return $admin;
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to create default admin: " . $e->getMessage(), 0, $e);
        }
    }

    public function validateCredentials(string $username, string $password): ?AdminUser
    {
        $admin = $this->findByUsername($username);

        if ($admin && $admin->verifyPassword($password)) {
            return $admin;
        }

        return null;
    }

    public function updatePassword(int $id, string $newPasswordHash): bool
    {
        try {
            $sql = "UPDATE admin_users SET password_hash = :password_hash, updated_at = NOW() WHERE id = :id";
            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute([
                ':password_hash' => $newPasswordHash,
                ':id' => $id
            ]);

            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to update admin password: " . $e->getMessage(), 0, $e);
        }
    }

    private function getNextId(): int
    {
        try {
            $sql = "SELECT COALESCE(MAX(id), 0) + 1 FROM admin_users";
            $stmt = $this->database->getConnection()->prepare($sql);
            $stmt->execute();

            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            throw new RuntimeException("Failed to get next admin ID: " . $e->getMessage(), 0, $e);
        }
    }

    private function rowToAdmin(array $row): AdminUser
    {
        return new AdminUser(
            (int) $row['id'],
            $row['username'],
            $row['password_hash'], // This is the hashed password
            $row['email'],
            new DateTimeImmutable($row['created_at']),
            new DateTimeImmutable($row['updated_at'])
        );
    }
}
