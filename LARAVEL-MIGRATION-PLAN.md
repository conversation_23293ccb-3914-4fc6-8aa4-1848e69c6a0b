# Laravel Style Klasör Düzeni - Geçiş Planı

## 🎯 He<PERSON><PERSON> Style Struktur

```
drxdion/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── AdminController.php
│   │   │   ├── OrderController.php
│   │   │   └── ApiController.php
│   │   ├── Middleware/
│   │   │   ├── AuthMiddleware.php
│   │   │   └── CsrfMiddleware.php
│   │   └── Requests/
│   ├── Models/
│   │   ├── Order.php
│   │   ├── AdminUser.php
│   │   └── Settings.php
│   ├── Services/
│   │   ├── OrderService.php
│   │   ├── PaymentService.php
│   │   └── StorageService.php
│   └── Repositories/
│       ├── OrderRepository.php
│       ├── AdminRepository.php
│       └── SettingsRepository.php
├── config/
│   ├── database.php
│   ├── app.php
│   └── services.php
├── database/
│   ├── migrations/
│   └── seeders/
├── resources/
│   ├── views/
│   │   ├── admin/
│   │   └── layouts/
│   └── assets/
│       ├── css/
│       └── js/
├── routes/
│   ├── web.php
│   └── api.php
├── storage/
│   ├── app/
│   ├── logs/
│   └── cache/
├── public/
│   ├── index.php
│   ├── assets/
│   └── uploads/
├── bootstrap/
│   └── app.php
├── vendor/
├── .env
├── composer.json
└── artisan (Custom CLI tool)
```

## 🔄 Geçiş Adımları

### 1. Temel Klasör Yapısını Oluştur
- [x] `app/` klasörü
- [x] `config/` klasörü  
- [x] `database/` klasörü
- [x] `resources/` klasörü
- [x] `routes/` klasörü
- [x] `bootstrap/` klasörü

### 2. Mevcut Dosyaları Taşı
- [x] `src/Models/` → `app/Models/`
- [ ] `src/Controllers/` → `app/Http/Controllers/`
- [ ] `src/Services/` → `app/Services/`
- [ ] `src/Database/` → `app/Repositories/`
- [ ] `public/admin/` → `resources/views/admin/`
- [x] `src/Config/` → `config/`

### 3. Namespace'leri Güncelle
- [x] `DrxDion\Models\` → `App\Models\`
- [ ] `DrxDion\Controllers\` → `App\Http\Controllers\`
- [ ] `DrxDion\Services\` → `App\Services\`
- [ ] `DrxDion\Database\` → `App\Repositories\`

### 4. Autoloader'ı Güncelle
- [x] `composer.json` PSR-4 mapping
- [x] `bootstrap/app.php` oluştur
- [x] `simple-config.php` → `public/index.php`

### 5. Routing Sistemi
- [x] `routes/web.php` oluştur
- [x] `routes/api.php` oluştur
- [ ] Route grupları ve middleware

### 6. Configuration
- [x] `config/database.php`
- [x] `config/app.php`
- [ ] Environment loading

## 🚀 Başlangıç
1. Temel klasör yapısını oluştur
2. Mevcut dosyaları taşı
3. Namespace'leri güncelle
4. Autoloader'ı test et
5. Routing'i test et

## ⚠️ Dikkat Edilecekler
- Tüm namespace'ler `App\` ile başlayacak
- PSR-4 autoloading standartları
- Laravel konvansiyonları
- Backward compatibility
