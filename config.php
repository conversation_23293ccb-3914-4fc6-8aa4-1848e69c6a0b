<?php

/**
 * DrxDion Bootstrap File
 * Clean Architecture Implementation
 */

declare(strict_types=1);

// Composer autoloader (for future use)
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
}

// Clean Architecture autoloader
spl_autoload_register(function (string $class): void {
    $prefix = 'DrxDion\\';
    $baseDir = __DIR__ . '/src/';

    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }

    $relativeClass = substr($class, $len);
    $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';

    if (file_exists($file)) {
        require_once $file;
    }
});

// Load services configuration
require_once __DIR__ . '/src/Config/services.php';

use DrxDion\Config\Container;

// Initialize environment
Environment::load(__DIR__ . '/.env');

// Initialize dependency injection container
$container = new Container();

// Register services
require_once __DIR__ . '/src/Infrastructure/DependencyInjection/services.php';
registerServices($container);

// Initialize middleware stack
$middlewareStack = new MiddlewareStack();

// Register middleware
require_once __DIR__ . '/src/Infrastructure/Web/Middleware/middleware.php';
registerMiddleware($middlewareStack, $container);

// Initialize router
$router = new Router($container, $middlewareStack);

// Register routes
require_once __DIR__ . '/src/Infrastructure/Web/Routes/routes.php';
registerRoutes($router, $container);
