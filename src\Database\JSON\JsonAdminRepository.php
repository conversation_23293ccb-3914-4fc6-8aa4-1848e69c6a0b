<?php

declare(strict_types=1);

namespace DrxDion\Database\JSON;

use DrxDion\Interfaces\AdminRepositoryInterface;
use DrxDion\Models\AdminUser;

/**
 * File-based Admin Repository
 * Fallback implementation when SQLite is not available
 */
final class JsonAdminRepository implements AdminRepositoryInterface
{
    private string $dataPath;

    public function __construct()
    {
        $this->dataPath = dirname(__DIR__, 4) . '/data';
        if (!is_dir($this->dataPath)) {
            mkdir($this->dataPath, 0755, true);
        }
    }

    public function save(AdminUser $admin): void
    {
        $admins = $this->loadAdmins();
        $admins[$admin->getUsername()] = $this->adminToArray($admin);
        $this->saveAdmins($admins);
    }

    public function findByUsername(string $username): ?AdminUser
    {
        $admins = $this->loadAdmins();
        return isset($admins[$username]) ? $this->arrayToAdmin($admins[$username]) : null;
    }

    public function findById(int $id): ?AdminUser
    {
        $admins = $this->loadAdmins();
        foreach ($admins as $admin) {
            if ($admin['id'] === $id) {
                return $this->arrayToAdmin($admin);
            }
        }
        return null;
    }

    public function hasAnyAdmins(): bool
    {
        $admins = $this->loadAdmins();
        return count($admins) > 0;
    }

    public function createDefaultAdmin(string $username, string $password, string $email): AdminUser
    {
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        $id = $this->getNextId();

        $admin = AdminUser::create($id, $username, $passwordHash, $email);
        $this->save($admin);

        return $admin;
    }

    public function validateCredentials(string $username, string $password): ?AdminUser
    {
        $admin = $this->findByUsername($username);

        if ($admin && $admin->verifyPassword($password)) {
            return $admin;
        }

        return null;
    }

    private function loadAdmins(): array
    {
        $filePath = $this->dataPath . '/admins.json';
        if (!file_exists($filePath)) {
            return [];
        }

        $data = file_get_contents($filePath);
        return $data ? json_decode($data, true) : [];
    }

    private function saveAdmins(array $admins): void
    {
        $filePath = $this->dataPath . '/admins.json';
        file_put_contents($filePath, json_encode($admins, JSON_PRETTY_PRINT));
    }

    private function getNextId(): int
    {
        $admins = $this->loadAdmins();
        $maxId = 0;
        foreach ($admins as $admin) {
            if ($admin['id'] > $maxId) {
                $maxId = $admin['id'];
            }
        }
        return $maxId + 1;
    }

    private function adminToArray(AdminUser $admin): array
    {
        return [
            'id' => $admin->getId(),
            'username' => $admin->getUsername(),
            'password' => $admin->getPasswordHash(),
            'email' => $admin->getEmail(),
            'created_at' => $admin->getCreatedAt()->format('Y-m-d H:i:s'),
            'updated_at' => $admin->getUpdatedAt()->format('Y-m-d H:i:s')
        ];
    }

    private function arrayToAdmin(array $data): AdminUser
    {
        return new AdminUser(
            (int)$data['id'],
            $data['username'],
            $data['password'],
            $data['email'],
            new \DateTimeImmutable($data['created_at']),
            new \DateTimeImmutable($data['updated_at'])
        );
    }
}
