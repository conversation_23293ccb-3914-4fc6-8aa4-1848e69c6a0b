document.addEventListener("DOMContentLoaded", function () {
	const settingsForm = document.getElementById("settingsForm");
	const messageDiv = document.getElementById("message");

	// Load current settings
	async function loadSettings() {
		try {
			// Check for admin token
			const adminToken = document.cookie
				.split(";")
				.find((cookie) => cookie.trim().startsWith("adminToken="));

			if (!adminToken) {
				window.location.href = "/admin/login";
				return;
			}
			console.log("Fetching settings...");
			const response = await fetch("/api/get-settings", {
				method: "GET",
				credentials: "include",
				headers: {
					Accept: "application/json",
				},
			});
			console.log("Response status:", response.status);
			if (response.status === 401) {
				window.location.href = "/admin/login";
				return;
			}

			if (!response.ok) {
				throw new Error("Failed to load settings");
			}

			const settings = await response.json();
			console.log("Settings received:", settings);

			if (settings.error) {
				throw new Error(settings.error);
			}

			// Populate form fields
			const usernameInput = document.getElementById("username");
			const emailInput = document.getElementById("email");
			const notificationEmailInput =
				document.getElementById("notification_email");

			if (usernameInput) usernameInput.value = settings.username || "";
			if (emailInput) emailInput.value = settings.email || "";
			if (notificationEmailInput)
				notificationEmailInput.value =
					settings.notificationEmail || settings.email || "";
		} catch (error) {
			messageDiv.textContent = "Error loading settings: " + error.message;
			messageDiv.className = "alert alert-danger";
			messageDiv.style.display = "block";
		}
	}

	// Load settings when page loads
	loadSettings();

	if (settingsForm) {
		settingsForm.addEventListener("submit", async function (e) {
			e.preventDefault();
			messageDiv.style.display = "none";

			try {
				const formData = {
					username: document.getElementById("username").value,
					email: document.getElementById("email").value,
					notification_email:
						document.getElementById("notification_email").value,
					currentPassword:
						document.getElementById("currentPassword").value,
					newPassword: document.getElementById("newPassword").value,
				};

				console.log("Updating settings...");
				const response = await fetch("/api/update-settings", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					credentials: "include",
					body: JSON.stringify(formData),
				});

				const data = await response.json();
				console.log("Update response:", data);

				if (!response.ok) {
					throw new Error(data.error || "Failed to update settings");
				}

				messageDiv.textContent = "Settings updated successfully!";
				messageDiv.className = "alert alert-success";
				messageDiv.style.display = "block";

				// Clear password fields
				document.getElementById("currentPassword").value = "";
				document.getElementById("newPassword").value = "";
			} catch (error) {
				console.error("Error:", error);
				messageDiv.textContent = error.message;
				messageDiv.className = "alert alert-danger";
				messageDiv.style.display = "block";
			}
		});
	}

	// Load current settings
	async function loadSettings() {
		try {
			const response = await fetch("/api/get-settings", {
				credentials: "same-origin",
				headers: {
					"X-CSRF-Token": csrfToken,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch settings");
			}

			const settings = await response.json();

			// Update form fields
			document.getElementById("username").value = settings.username || "";
			document.getElementById("email").value = settings.email || "";
			document.getElementById("notification_email").value =
				settings.notificationEmail || "";
		} catch (error) {
			messageDiv.textContent = "Error loading settings: " + error.message;
			messageDiv.className = "alert alert-danger";
		}
	}

	// Load settings when page loads
	loadSettings();
});
