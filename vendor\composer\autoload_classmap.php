<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'App\\Http\\Controllers\\AdminController' => $baseDir . '/app/Http/Controllers/AdminController.php',
    'App\\Http\\Controllers\\ApiController' => $baseDir . '/app/Http/Controllers/ApiController.php',
    'App\\Http\\Controllers\\Controller' => $baseDir . '/app/Http/Controllers/Controller.php',
    'App\\Http\\Controllers\\PageController' => $baseDir . '/app/Http/Controllers/PageController.php',
    'App\\Models\\AdminUser' => $baseDir . '/app/Models/AdminUser.php',
    'App\\Models\\Order' => $baseDir . '/app/Models/Order.php',
    'App\\Models\\Settings' => $baseDir . '/app/Models/Settings.php',
    'App\\Services\\StorageService' => $baseDir . '/app/Services/StorageService.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'DrxDion\\Config\\Container' => $baseDir . '/src/Config/Container.php',
    'DrxDion\\Controllers\\AdminController' => $baseDir . '/src/Controllers/AdminController.php',
    'DrxDion\\Controllers\\PageController' => $baseDir . '/src/Controllers/PageController.php',
    'DrxDion\\Database\\Composite\\CompositeOrderRepository' => $baseDir . '/src/Database/Composite/CompositeOrderRepository.php',
    'DrxDion\\Database\\JSON\\JsonAdminRepository' => $baseDir . '/src/Database/JSON/JsonAdminRepository.php',
    'DrxDion\\Database\\JSON\\JsonOrderRepository' => $baseDir . '/src/Database/JSON/JsonOrderRepository.php',
    'DrxDion\\Database\\MySQL\\MySQLAdminRepository' => $baseDir . '/src/Database/MySQL/MySQLAdminRepository.php',
    'DrxDion\\Database\\MySQL\\MySQLConnection' => $baseDir . '/src/Database/MySQL/MySQLConnection.php',
    'DrxDion\\Database\\MySQL\\MySQLOrderRepository' => $baseDir . '/src/Database/MySQL/MySQLOrderRepository.php',
    'DrxDion\\Database\\MySQL\\MySQLSettingsRepository' => $baseDir . '/src/Database/MySQL/MySQLSettingsRepository.php',
    'DrxDion\\Database\\Smart\\SmartOrderRepository' => $baseDir . '/src/Database/Smart/SmartOrderRepository.php',
    'DrxDion\\Interfaces\\AdminRepositoryInterface' => $baseDir . '/src/Interfaces/AdminRepositoryInterface.php',
    'DrxDion\\Interfaces\\EmailServiceInterface' => $baseDir . '/src/Interfaces/EmailServiceInterface.php',
    'DrxDion\\Interfaces\\OrderRepositoryInterface' => $baseDir . '/src/Interfaces/OrderRepositoryInterface.php',
    'DrxDion\\Interfaces\\PaymentGatewayInterface' => $baseDir . '/src/Interfaces/PaymentGatewayInterface.php',
    'DrxDion\\Interfaces\\SecurityServiceInterface' => $baseDir . '/src/Interfaces/SecurityServiceInterface.php',
    'DrxDion\\Interfaces\\SettingsRepositoryInterface' => $baseDir . '/src/Interfaces/SettingsRepositoryInterface.php',
    'DrxDion\\Interfaces\\StorageRepositoryInterface' => $baseDir . '/src/Interfaces/StorageRepositoryInterface.php',
    'DrxDion\\Models\\AdminUser' => $baseDir . '/src/Models/AdminUser.php',
    'DrxDion\\Models\\Order' => $baseDir . '/src/Models/Order.php',
    'DrxDion\\Models\\Settings' => $baseDir . '/src/Models/Settings.php',
    'DrxDion\\Services\\StorageService' => $baseDir . '/src/Services/StorageService.php',
);
