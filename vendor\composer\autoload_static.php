<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitdec9b3f7fc6f47fb92bd3a2ae1fa65e7
{
    public static $prefixLengthsPsr4 = array (
        'D' => 
        array (
            'DrxDion\\' => 8,
        ),
        'A' => 
        array (
            'App\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'DrxDion\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
        'App\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $classMap = array (
        'App\\Http\\Controllers\\AdminController' => __DIR__ . '/../..' . '/app/Http/Controllers/AdminController.php',
        'App\\Http\\Controllers\\ApiController' => __DIR__ . '/../..' . '/app/Http/Controllers/ApiController.php',
        'App\\Http\\Controllers\\Controller' => __DIR__ . '/../..' . '/app/Http/Controllers/Controller.php',
        'App\\Http\\Controllers\\PageController' => __DIR__ . '/../..' . '/app/Http/Controllers/PageController.php',
        'App\\Models\\AdminUser' => __DIR__ . '/../..' . '/app/Models/AdminUser.php',
        'App\\Models\\Order' => __DIR__ . '/../..' . '/app/Models/Order.php',
        'App\\Models\\Settings' => __DIR__ . '/../..' . '/app/Models/Settings.php',
        'App\\Services\\StorageService' => __DIR__ . '/../..' . '/app/Services/StorageService.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'DrxDion\\Config\\Container' => __DIR__ . '/../..' . '/src/Config/Container.php',
        'DrxDion\\Controllers\\AdminController' => __DIR__ . '/../..' . '/src/Controllers/AdminController.php',
        'DrxDion\\Controllers\\PageController' => __DIR__ . '/../..' . '/src/Controllers/PageController.php',
        'DrxDion\\Database\\Composite\\CompositeOrderRepository' => __DIR__ . '/../..' . '/src/Database/Composite/CompositeOrderRepository.php',
        'DrxDion\\Database\\JSON\\JsonAdminRepository' => __DIR__ . '/../..' . '/src/Database/JSON/JsonAdminRepository.php',
        'DrxDion\\Database\\JSON\\JsonOrderRepository' => __DIR__ . '/../..' . '/src/Database/JSON/JsonOrderRepository.php',
        'DrxDion\\Database\\MySQL\\MySQLAdminRepository' => __DIR__ . '/../..' . '/src/Database/MySQL/MySQLAdminRepository.php',
        'DrxDion\\Database\\MySQL\\MySQLConnection' => __DIR__ . '/../..' . '/src/Database/MySQL/MySQLConnection.php',
        'DrxDion\\Database\\MySQL\\MySQLOrderRepository' => __DIR__ . '/../..' . '/src/Database/MySQL/MySQLOrderRepository.php',
        'DrxDion\\Database\\MySQL\\MySQLSettingsRepository' => __DIR__ . '/../..' . '/src/Database/MySQL/MySQLSettingsRepository.php',
        'DrxDion\\Database\\Smart\\SmartOrderRepository' => __DIR__ . '/../..' . '/src/Database/Smart/SmartOrderRepository.php',
        'DrxDion\\Interfaces\\AdminRepositoryInterface' => __DIR__ . '/../..' . '/src/Interfaces/AdminRepositoryInterface.php',
        'DrxDion\\Interfaces\\EmailServiceInterface' => __DIR__ . '/../..' . '/src/Interfaces/EmailServiceInterface.php',
        'DrxDion\\Interfaces\\OrderRepositoryInterface' => __DIR__ . '/../..' . '/src/Interfaces/OrderRepositoryInterface.php',
        'DrxDion\\Interfaces\\PaymentGatewayInterface' => __DIR__ . '/../..' . '/src/Interfaces/PaymentGatewayInterface.php',
        'DrxDion\\Interfaces\\SecurityServiceInterface' => __DIR__ . '/../..' . '/src/Interfaces/SecurityServiceInterface.php',
        'DrxDion\\Interfaces\\SettingsRepositoryInterface' => __DIR__ . '/../..' . '/src/Interfaces/SettingsRepositoryInterface.php',
        'DrxDion\\Interfaces\\StorageRepositoryInterface' => __DIR__ . '/../..' . '/src/Interfaces/StorageRepositoryInterface.php',
        'DrxDion\\Models\\AdminUser' => __DIR__ . '/../..' . '/src/Models/AdminUser.php',
        'DrxDion\\Models\\Order' => __DIR__ . '/../..' . '/src/Models/Order.php',
        'DrxDion\\Models\\Settings' => __DIR__ . '/../..' . '/src/Models/Settings.php',
        'DrxDion\\Services\\StorageService' => __DIR__ . '/../..' . '/src/Services/StorageService.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitdec9b3f7fc6f47fb92bd3a2ae1fa65e7::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitdec9b3f7fc6f47fb92bd3a2ae1fa65e7::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitdec9b3f7fc6f47fb92bd3a2ae1fa65e7::$classMap;

        }, null, ClassLoader::class);
    }
}
