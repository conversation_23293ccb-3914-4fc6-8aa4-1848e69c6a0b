<?php

declare(strict_types=1);

namespace DrxDion\Database\Smart;

use DrxDion\Interfaces\OrderRepositoryInterface;
use DrxDion\Models\Order;
use DrxDion\Infrastructure\Persistence\MySQL\MySQLOrderRepository;
use DrxDion\Infrastructure\Persistence\File\JsonOrderRepository;

/**
 * Smart Order Repository with Automatic Fallback
 * Uses MySQL when available, falls back to JSON when MySQL is unavailable
 */
final class SmartOrderRepository implements OrderRepositoryInterface
{
    private bool $mysqlAvailable = false;
    private ?MySQLOrderRepository $mysqlRepository = null;
    private JsonOrderRepository $jsonRepository;

    public function __construct(
        ?MySQLOrderRepository $mysqlRepository,
        JsonOrderRepository $jsonRepository
    ) {
        $this->jsonRepository = $jsonRepository;

        if ($mysqlRepository !== null) {
            $this->mysqlRepository = $mysqlRepository;
            $this->mysqlAvailable = $this->testMySQLConnection();
        }
    }

    private function testMySQLConnection(): bool
    {
        try {
            // Test MySQL connection by doing a simple count
            $this->mysqlRepository->count();
            return true;
        } catch (\Exception $e) {
            error_log("MySQL connection failed, falling back to JSON: " . $e->getMessage());
            return false;
        }
    }

    private function ensureMySQLAvailable(): bool
    {
        if ($this->mysqlRepository === null) {
            return false;
        }

        // Re-test connection if previously failed
        if (!$this->mysqlAvailable) {
            $this->mysqlAvailable = $this->testMySQLConnection();
        }

        return $this->mysqlAvailable;
    }

    public function save(Order $order): void
    {
        $mysqlSaved = false;

        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                $this->mysqlRepository->save($order);
                $mysqlSaved = true;
            } catch (\Exception $e) {
                error_log("MySQL save failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false; // Mark as unavailable
            }
        }

        // Always save to JSON (as backup if MySQL worked, as primary if MySQL failed)
        try {
            $this->jsonRepository->save($order);
        } catch (\Exception $e) {
            // If MySQL also failed, this is critical
            if (!$mysqlSaved) {
                throw new \RuntimeException("Both MySQL and JSON save failed: " . $e->getMessage());
            }
            // If MySQL succeeded, just log JSON error
            error_log("JSON backup save failed: " . $e->getMessage());
        }
    }

    public function findById(string $id): ?Order
    {
        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                return $this->mysqlRepository->findById($id);
            } catch (\Exception $e) {
                error_log("MySQL findById failed, trying JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Fallback to JSON
        return $this->jsonRepository->findById($id);
    }

    public function findAll(int $limit = 100, int $offset = 0): array
    {
        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                return $this->mysqlRepository->findAll($limit, $offset);
            } catch (\Exception $e) {
                error_log("MySQL findAll failed, trying JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Fallback to JSON
        return $this->jsonRepository->findAll($limit, $offset);
    }

    public function updateStatus(string $id, string $status): bool
    {
        $mysqlSuccess = false;

        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                $mysqlSuccess = $this->mysqlRepository->updateStatus($id, $status);
            } catch (\Exception $e) {
                error_log("MySQL updateStatus failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Update JSON
        try {
            $jsonSuccess = $this->jsonRepository->updateStatus($id, $status);
            return $mysqlSuccess || $jsonSuccess;
        } catch (\Exception $e) {
            if (!$mysqlSuccess) {
                throw new \RuntimeException("Both MySQL and JSON update failed: " . $e->getMessage());
            }
            error_log("JSON update failed: " . $e->getMessage());
            return $mysqlSuccess;
        }
    }

    public function updatePayment(string $id, ?string $paymentId, string $status): bool
    {
        $mysqlSuccess = false;

        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                $mysqlSuccess = $this->mysqlRepository->updatePayment($id, $paymentId, $status);
            } catch (\Exception $e) {
                error_log("MySQL updatePayment failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Update JSON
        try {
            $jsonSuccess = $this->jsonRepository->updatePayment($id, $paymentId, $status);
            return $mysqlSuccess || $jsonSuccess;
        } catch (\Exception $e) {
            if (!$mysqlSuccess) {
                throw new \RuntimeException("Both MySQL and JSON update failed: " . $e->getMessage());
            }
            error_log("JSON update failed: " . $e->getMessage());
            return $mysqlSuccess;
        }
    }

    public function delete(string $id): bool
    {
        $mysqlSuccess = false;

        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                $mysqlSuccess = $this->mysqlRepository->delete($id);
            } catch (\Exception $e) {
                error_log("MySQL delete failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Delete from JSON
        try {
            $jsonSuccess = $this->jsonRepository->delete($id);
            return $mysqlSuccess || $jsonSuccess;
        } catch (\Exception $e) {
            if (!$mysqlSuccess) {
                throw new \RuntimeException("Both MySQL and JSON delete failed: " . $e->getMessage());
            }
            error_log("JSON delete failed: " . $e->getMessage());
            return $mysqlSuccess;
        }
    }

    public function count(): int
    {
        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                return $this->mysqlRepository->count();
            } catch (\Exception $e) {
                error_log("MySQL count failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Fallback to JSON
        return $this->jsonRepository->count();
    }

    public function countWithCriteria(array $criteria): int
    {
        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                return $this->mysqlRepository->countWithCriteria($criteria);
            } catch (\Exception $e) {
                error_log("MySQL countWithCriteria failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Fallback to JSON
        return $this->jsonRepository->countWithCriteria($criteria);
    }

    public function search(array $criteria, int $limit = 20, int $offset = 0): array
    {
        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                return $this->mysqlRepository->search($criteria, $limit, $offset);
            } catch (\Exception $e) {
                error_log("MySQL search failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Fallback to JSON
        return $this->jsonRepository->search($criteria, $limit, $offset);
    }

    public function findByStatus(string $status, int $limit = 100): array
    {
        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                return $this->mysqlRepository->findByStatus($status, $limit);
            } catch (\Exception $e) {
                error_log("MySQL findByStatus failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Fallback to JSON
        return $this->jsonRepository->findByStatus($status, $limit);
    }

    public function countByStatus(string $status): int
    {
        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                return $this->mysqlRepository->countByStatus($status);
            } catch (\Exception $e) {
                error_log("MySQL countByStatus failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Fallback to JSON
        return $this->jsonRepository->countByStatus($status);
    }

    public function countAll(): int
    {
        // Try MySQL first if available
        if ($this->ensureMySQLAvailable()) {
            try {
                return $this->mysqlRepository->countAll();
            } catch (\Exception $e) {
                error_log("MySQL countAll failed, using JSON: " . $e->getMessage());
                $this->mysqlAvailable = false;
            }
        }

        // Fallback to JSON
        return $this->jsonRepository->countAll();
    }

    /**
     * Get current storage status with consistency check
     */
    public function getStorageStatus(): array
    {
        $status = [
            'mysql_available' => $this->mysqlAvailable,
            'mysql_configured' => $this->mysqlRepository !== null,
            'json_available' => true, // JSON is always available if file system works
            'primary_storage' => $this->mysqlAvailable ? 'mysql' : 'json'
        ];

        // Add consistency check if both storages are available
        if ($this->mysqlAvailable && $this->mysqlRepository !== null) {
            try {
                $mysqlCount = $this->mysqlRepository->countAll();
                $jsonCount = $this->jsonRepository->countAll();

                $status['consistency'] = [
                    'mysql_count' => $mysqlCount,
                    'json_count' => $jsonCount,
                    'is_consistent' => $mysqlCount === $jsonCount
                ];
            } catch (\Exception $e) {
                error_log("Error checking storage consistency: " . $e->getMessage());
                $status['consistency'] = [
                    'error' => 'Unable to check consistency: ' . $e->getMessage()
                ];
            }
        }

        return $status;
    }

    /**
     * Sync orders from MySQL to JSON
     * Returns sync statistics
     */
    public function syncFromMySQLToJSON(): array
    {
        if (!$this->mysqlAvailable || $this->mysqlRepository === null) {
            throw new \RuntimeException('MySQL is not available for sync');
        }

        $result = [
            'new_orders_synced' => 0,
            'orders_updated' => 0,
            'errors' => 0,
            'json_orders_before' => $this->jsonRepository->countAll(),
            'mysql_orders' => $this->mysqlRepository->countAll(),
            'json_orders_after' => 0
        ];

        try {
            // Get all orders from MySQL in batches
            $limit = 100;
            $offset = 0;
            $totalProcessed = 0;

            while (true) {
                $mysqlOrders = $this->mysqlRepository->findAll($limit, $offset);

                if (empty($mysqlOrders)) {
                    break; // No more orders to process
                }

                foreach ($mysqlOrders as $order) {
                    try {
                        $existingOrder = $this->jsonRepository->findById($order->getId());

                        if ($existingOrder === null) {
                            // New order, save to JSON
                            $this->jsonRepository->save($order);
                            $result['new_orders_synced']++;
                        } else {
                            // Update existing order in JSON
                            $this->jsonRepository->save($order);
                            $result['orders_updated']++;
                        }
                    } catch (\Exception $e) {
                        error_log("Error syncing order {$order->getId()}: " . $e->getMessage());
                        $result['errors']++;
                    }
                }

                $totalProcessed += count($mysqlOrders);
                $offset += $limit;

                // Safety check to prevent infinite loops
                if ($totalProcessed >= 10000) {
                    error_log("Sync stopped at 10,000 orders for safety");
                    break;
                }
            }

            $result['json_orders_after'] = $this->jsonRepository->countAll();
        } catch (\Exception $e) {
            error_log("Sync error: " . $e->getMessage());
            throw new \RuntimeException('Sync failed: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * Replace JSON with MySQL data (full sync)
     * This will completely overwrite JSON with MySQL data
     */
    public function replaceJSONWithMySQL(): array
    {
        if (!$this->mysqlAvailable || $this->mysqlRepository === null) {
            throw new \RuntimeException('MySQL is not available for sync');
        }

        $result = [
            'json_orders_before' => $this->jsonRepository->countAll(),
            'mysql_orders' => $this->mysqlRepository->countAll(),
            'json_orders_after' => 0,
            'operation' => 'replace'
        ];

        try {
            // Get all orders from MySQL
            $mysqlOrders = $this->mysqlRepository->findAll(10000, 0); // Get all orders

            // Clear JSON file and rebuild from MySQL
            $jsonFile = dirname(__DIR__, 4) . '/data/orders.json';
            $newJsonData = [];

            foreach ($mysqlOrders as $order) {
                $newJsonData[$order->getId()] = [
                    'id' => $order->getId(),
                    'product_name' => $order->getProductName(),
                    'price' => $order->getPrice(),
                    'total_price' => $order->getTotalPrice(),
                    'shipping_cost' => $order->getShippingCost(),
                    'payment_method' => $order->getPaymentMethod(),
                    'payment_status' => $order->getPaymentStatus(),
                    'payment_id' => $order->getPaymentId(),
                    'customer_name' => $order->getCustomerName(),
                    'customer_email' => $order->getCustomerEmail(),
                    'shipping_address' => $order->getShippingAddress(),
                    'created_at' => $order->getCreatedAt()->format('Y-m-d H:i:s'),
                    'updated_at' => $order->getUpdatedAt()->format('Y-m-d H:i:s')
                ];
            }

            // Write new JSON data
            file_put_contents($jsonFile, json_encode($newJsonData, JSON_PRETTY_PRINT));

            $result['json_orders_after'] = count($newJsonData);
        } catch (\Exception $e) {
            error_log("Replace sync error: " . $e->getMessage());
            throw new \RuntimeException('Replace sync failed: ' . $e->getMessage());
        }

        return $result;
    }
}
