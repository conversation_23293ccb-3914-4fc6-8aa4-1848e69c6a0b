<?php

declare(strict_types=1);

/**
 * Database Initialization Script
 * PHP equivalent of npm run init-db
 */

require_once __DIR__ . '/../config.php';

try {
    echo "🔄 Initializing DrxDion database...\n";

    // Get database connection from container
    $container = new \DrxDion\Infrastructure\DependencyInjection\Container();
    require_once __DIR__ . '/../src/Infrastructure/DependencyInjection/services.php';
    registerServices($container);

    $database = $container->get(\DrxDion\Infrastructure\Persistence\SQLite\DatabaseConnection::class);

    echo "✅ Database initialized successfully!\n";
    echo "📁 Database location: " . \DrxDion\Infrastructure\Config\Environment::get('database.path') . "\n";

    // Check if admin user exists
    $adminRepo = $container->get(\DrxDion\Domain\Contracts\AdminRepositoryInterface::class);

    if (!$adminRepo->hasAnyAdmins()) {
        echo "⚠️  No admin users found. Creating default admin...\n";

        $defaultUsername = 'admin';
        $defaultPassword = bin2hex(random_bytes(8));
        $defaultEmail = '<EMAIL>';

        $admin = $adminRepo->createDefaultAdmin($defaultUsername, $defaultPassword, $defaultEmail);

        echo "✅ Default admin user created!\n";
        echo "👤 Username: {$defaultUsername}\n";
        echo "🔑 Password: {$defaultPassword}\n";
        echo "📧 Email: {$defaultEmail}\n";
        echo "⚠️  Please change this password after first login!\n";
    } else {
        echo "✅ Admin users already exist.\n";
    }
} catch (Throwable $e) {
    echo "❌ Database initialization failed: " . $e->getMessage() . "\n";
    exit(1);
}
