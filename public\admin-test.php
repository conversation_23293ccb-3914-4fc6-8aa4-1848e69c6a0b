<?php
echo "Admin test page working!<br>";
echo "Current URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "Method: " . $_SERVER['REQUEST_METHOD'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";

// Test if bootstrap works
try {
    require_once __DIR__ . '/../bootstrap/app.php';
    echo "Bootstrap loaded successfully!<br>";
    echo "BASE_PATH: " . BASE_PATH . "<br>";
} catch (Exception $e) {
    echo "Bootstrap error: " . $e->getMessage() . "<br>";
}

// Test if routes work
try {
    $webRoutes = require BASE_PATH . '/routes/web.php';
    echo "Routes loaded: " . count($webRoutes) . " routes<br>";
    foreach ($webRoutes as $uri => $route) {
        echo "Route: $uri => {$route[0]} {$route[1]}<br>";
    }
} catch (Exception $e) {
    echo "Routes error: " . $e->getMessage() . "<br>";
}
