<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Orders - Admin Panel</title>
		<link href="/assets/css/bootstrap.min.css" rel="stylesheet" />
		<link rel="stylesheet" href="/assets/css/bootstrap-icons.css" />
		<style>
			body {
				background-color: #f8f9fa;
				padding: 2rem;
			}
			.orders-table {
				background: white;
				border-radius: 10px;
				box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
			}
			.table th {
				border-top: none;
				background-color: #f8f9fa;
			}
			.badge.payment-status {
				font-size: 0.875rem;
				padding: 0.5rem 0.75rem;
			}
			.status-pending {
				background-color: #ffc107;
			}
			.status-paid {
				background-color: #198754;
			}
			.status-failed {
				background-color: #dc3545;
			}			.form-select.status-select {
				padding: 0.25rem 2rem 0.25rem 0.5rem;
				font-size: 0.875rem;
				border-radius: 4px;
			}
			.admin-header {
				background: #f8f9fa;
				padding: 1rem 0;
				margin-bottom: 2rem;
				border-bottom: 1px solid #dee2e6;
			}
			.toast-container {
				z-index: 1050;
			}
		</style>
	</head>
	<body>
		<header class="admin-header">
			<div class="container">
				<div class="d-flex justify-content-between align-items-center">					<h1 class="h4 mb-0">DrxDion Admin</h1>
					<div>
						<a href="/admin/orders" class="btn btn-outline-primary me-2">Orders</a>
						<a href="/admin/settings" class="btn btn-outline-secondary me-2">Settings</a>
						<a href="/admin/logout" class="btn btn-outline-danger">Logout</a>
					</div>
				</div>
			</div>
		</header>

		<div class="container">
			<div class="d-flex justify-content-between align-items-center mb-4">
				<h2 class="mb-0">Orders</h2>
				<form class="d-flex" action="/admin/orders" method="GET">
					<div class="input-group">
						<input 
							type="search" 
							class="form-control" 
							placeholder="Search orders..." 
							name="search"
							value="<%= typeof searchTerm !== 'undefined' ? searchTerm : '' %>"
						>
						<button class="btn btn-outline-primary" type="submit">
							<i class="bi bi-search"></i>
						</button>
						<% if (typeof searchTerm !== 'undefined' && searchTerm) { %>
							<a href="/admin/orders" class="btn btn-outline-secondary">
								<i class="bi bi-x-lg"></i>
							</a>
						<% } %>
					</div>
				</form>
			</div>
			<div class="orders-table table-responsive">
				<table class="table table-hover mb-0">					<thead>						<tr>							<th>Order ID</th>
							<th>Product</th>
							<th>Shipping Address</th>
							<th>Total</th>
							<th>Shipping</th>
							<th>Payment Method</th>
							<th>Status</th>
							<th>Date</th>
							<th>Actions</th>
						</tr>
					</thead>
					<tbody>
						<% orders.forEach(order => { %>
						<tr>
							<td><%= order.id %></td>
							<td><%= order.product_name %></td>							<td>
								<div><%= order.shipping_address || 'N/A' %></div>
							</td>							<td>$<%= order.total_price %></td>
							<td>
								<% if (order.shipping_cost && order.shipping_cost > 0) { %>
									$<%= order.shipping_cost %>
								<% } else { %>
									<span class="text-success">Free</span>
								<% } %>
							</td>
							<td><%= order.payment_method === 'card' ? 'Credit Card' : 'Cash on Delivery' %></td><td>
								<select 
									class="form-select form-select-sm status-select" 
									data-order-id="<%= order.id %>"
									style="max-width: 140px;"
								>
									<option value="pending" <%= order.payment_status === 'pending' ? 'selected' : '' %>>Pending</option>
									<option value="paid" <%= order.payment_status === 'paid' ? 'selected' : '' %>>Paid</option>
									<option value="shipped" <%= order.payment_status === 'shipped' ? 'selected' : '' %>>Shipped</option>
									<option value="delivered" <%= order.payment_status === 'delivered' ? 'selected' : '' %>>Delivered</option>
								</select>
							</td>							<td>
								<%= new Date(order.created_at).toLocaleDateString() %>
								<small class="text-muted d-block"><%= new Date(order.created_at).toLocaleTimeString() %></small>
							</td>
							<td>
								<button 
									class="btn btn-sm btn-outline-danger delete-order-btn" 
									data-order-id="<%= order.id %>"
									title="Delete Order"
								>
									<i class="bi bi-trash"></i>
								</button>
							</td>
						</tr>
						<% }); %>
					</tbody>
				</table>
			</div>
			
			<!-- Pagination -->
			<% if (totalPages > 1) { %>
			<nav aria-label="Orders pagination" class="mt-4">
				<ul class="pagination justify-content-center">					<li class="page-item <%= currentPage === 1 ? 'disabled' : '' %>">
						<a class="page-link" href="/admin/orders?page=<%= currentPage - 1 %><%= searchTerm ? '&search=' + searchTerm : '' %>" <%= currentPage === 1 ? 'tabindex="-1" aria-disabled="true"' : '' %>>Previous</a>
					</li>
					
					<% for(let i = 1; i <= totalPages; i++) { %>
						<li class="page-item <%= currentPage === i ? 'active' : '' %>">
							<a class="page-link" href="/admin/orders?page=<%= i %><%= searchTerm ? '&search=' + searchTerm : '' %>"><%= i %></a>
						</li>
					<% } %>
					
					<li class="page-item <%= currentPage === totalPages ? 'disabled' : '' %>">
						<a class="page-link" href="/admin/orders?page=<%= currentPage + 1 %><%= searchTerm ? '&search=' + searchTerm : '' %>" <%= currentPage === totalPages ? 'tabindex="-1" aria-disabled="true"' : '' %>>Next</a>
					</li>
				</ul>
			</nav>
			
			<div class="text-center text-muted mt-2">
				Showing <%= (currentPage - 1) * 10 + 1 %>-<%= Math.min(currentPage * 10, total) %> of <%= total %> orders
			</div>
			<% } %>
		</div>
		<script src="/assets/js/bootstrap.bundle.min.js"></script>		<script>
		document.addEventListener('DOMContentLoaded', function() {
			// Function to show toast notification
			function showToast(message, type) {
				type = type || 'success';
				var bgClass = type === 'success' ? 'bg-success' : 'bg-danger';
				
				var toastHtml = 
					'<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">' +
						'<div class="toast align-items-center text-white ' + bgClass + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">' +
							'<div class="d-flex">' +
								'<div class="toast-body">' + message + '</div>' +
								'<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>' +
							'</div>' +
						'</div>' +
					'</div>';
				
				document.body.insertAdjacentHTML('beforeend', toastHtml);
				var toastEl = document.querySelector('.toast-container:last-child .toast');
				var toast = new bootstrap.Toast(toastEl, { delay: 3000 });
				toast.show();
				
				// Remove toast container after it's hidden
				toastEl.addEventListener('hidden.bs.toast', function() {
					var container = toastEl.closest('.toast-container');
					if (container) {
						container.remove();
					}
				});
			}
			
			// Function to capitalize first letter and format status
			function formatStatus(status) {
				var statusMap = {
					'pending': 'Pending',
					'paid': 'Paid',
					'shipped': 'Shipped',
					'delivered': 'Delivered'
				};
				return statusMap[status] || status;
			}
			
			// Status update handler
			document.querySelectorAll('.status-select').forEach(function(select) {
				// Store original value
				select.setAttribute('data-original-value', select.value);
				
				select.addEventListener('change', function() {
					var orderId = this.dataset.orderId;
					var status = this.value;
					var originalValue = this.getAttribute('data-original-value');
					var selectElement = this;
					var formattedStatus = formatStatus(status);
					
					console.log('Updating order:', orderId, 'to status:', status);
					
					// Make the request
					fetch('/admin/orders/' + orderId + '/status', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({ status: status })
					})
					.then(function(response) {
						console.log('Response status:', response.status);
						if (response.ok) {
							return response.json();
						} else {
							throw new Error('Network response was not ok: ' + response.status);
						}
					})
					.then(function(result) {
						console.log('Response result:', result);
						if (result.success) {
							showToast('Order status updated to "' + formattedStatus + '" successfully!', 'success');
							// Update the stored original value
							selectElement.setAttribute('data-original-value', status);
						} else {
							throw new Error('Update failed');
						}
					})
					.catch(function(error) {
						console.error('Error updating status:', error);
						showToast('Failed to update order status: ' + error.message, 'error');
						// Revert to original value
						selectElement.value = originalValue;
					});				});
			});
			
			// Delete order handler
			document.querySelectorAll('.delete-order-btn').forEach(function(button) {
				button.addEventListener('click', function() {
					var orderId = this.dataset.orderId;
					var row = this.closest('tr');
					
					if (confirm('Are you sure you want to delete this order? This action cannot be undone.')) {
						console.log('Deleting order:', orderId);
						
						fetch('/admin/orders/' + orderId, {
							method: 'DELETE',
							headers: {
								'Content-Type': 'application/json'
							}
						})
						.then(function(response) {
							console.log('Delete response status:', response.status);
							if (response.ok) {
								return response.json();
							} else {
								throw new Error('Network response was not ok: ' + response.status);
							}
						})
						.then(function(result) {
							console.log('Delete response result:', result);
							if (result.success) {
								showToast('Order deleted successfully!', 'success');
								// Remove the row from table
								row.remove();
								
								// If no more rows, show empty message or refresh page
								var tableBody = document.querySelector('tbody');
								if (tableBody.children.length === 0) {
									location.reload();
								}
							} else {
								throw new Error('Delete failed');
							}
						})
						.catch(function(error) {
							console.error('Error deleting order:', error);
							showToast('Failed to delete order: ' + error.message, 'error');
						});
					}
				});
			});
		});
		</script>
	</body>
</html>
