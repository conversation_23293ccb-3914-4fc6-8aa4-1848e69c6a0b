<?php

declare(strict_types=1);

namespace DrxDion\Database\MySQL;

use DrxDion\Interfaces\SettingsRepositoryInterface;
use DrxDion\Models\Settings;
use PDO;
use PDOException;

/**
 * MySQL Settings Repository
 * Implements settings operations using MySQL database
 */
final class MySQLSettingsRepository implements SettingsRepositoryInterface
{
    public function __construct(
        private readonly MySQLConnection $connection
    ) {
        $this->ensureTable();
    }

    public function getAll(): array
    {
        try {
            $pdo = $this->connection->getConnection();
            $stmt = $pdo->query('SELECT * FROM settings ORDER BY category, key');

            $settings = [];
            foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $row) {
                $settings[$row['key']] = Settings::fromArray($row);
            }

            return $settings;
        } catch (PDOException $e) {
            error_log("MySQL Settings error in getAll: " . $e->getMessage());
            return [];
        }
    }

    public function get(string $key): ?Settings
    {
        try {
            $pdo = $this->connection->getConnection();
            $stmt = $pdo->prepare('SELECT * FROM settings WHERE key = ?');
            $stmt->execute([$key]);

            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!$row) {
                return null;
            }

            return Settings::fromArray($row);
        } catch (PDOException $e) {
            error_log("MySQL Settings error in get: " . $e->getMessage());
            return null;
        }
    }

    public function set(string $key, string $value, string $type = 'string', string $description = ''): bool
    {
        try {
            $pdo = $this->connection->getConnection();

            // Try to update first
            $stmt = $pdo->prepare('
                UPDATE settings 
                SET value = ?, type = ?, description = ?, updated_at = NOW() 
                WHERE key = ?
            ');
            $stmt->execute([$value, $type, $description, $key]);

            // If no rows affected, insert new record
            if ($stmt->rowCount() === 0) {
                $stmt = $pdo->prepare('
                    INSERT INTO settings (key, value, type, description, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, NOW(), NOW())
                ');
                $stmt->execute([$key, $value, $type, $description]);
            }

            return true;
        } catch (PDOException $e) {
            error_log("MySQL Settings error in set: " . $e->getMessage());
            return false;
        }
    }

    public function updateMultiple(array $settings): bool
    {
        try {
            $pdo = $this->connection->getConnection();
            $pdo->beginTransaction();

            foreach ($settings as $key => $value) {
                $type = 'string';
                $description = '';

                // Handle array format
                if (is_array($value)) {
                    $actualValue = $value['value'] ?? '';
                    $type = $value['type'] ?? 'string';
                    $description = $value['description'] ?? '';
                } else {
                    $actualValue = (string) $value;
                }

                if (!$this->set($key, $actualValue, $type, $description)) {
                    $pdo->rollBack();
                    return false;
                }
            }

            $pdo->commit();
            return true;
        } catch (PDOException $e) {
            $pdo->rollBack();
            error_log("MySQL Settings error in updateMultiple: " . $e->getMessage());
            return false;
        }
    }

    public function delete(string $key): bool
    {
        try {
            $pdo = $this->connection->getConnection();
            $stmt = $pdo->prepare('DELETE FROM settings WHERE key = ?');
            $stmt->execute([$key]);

            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("MySQL Settings error in delete: " . $e->getMessage());
            return false;
        }
    }

    public function exists(string $key): bool
    {
        try {
            $pdo = $this->connection->getConnection();
            $stmt = $pdo->prepare('SELECT 1 FROM settings WHERE key = ? LIMIT 1');
            $stmt->execute([$key]);

            return $stmt->fetch() !== false;
        } catch (PDOException $e) {
            error_log("MySQL Settings error in exists: " . $e->getMessage());
            return false;
        }
    }

    public function getByCategory(string $category): array
    {
        try {
            $pdo = $this->connection->getConnection();
            $stmt = $pdo->prepare('SELECT * FROM settings WHERE category = ? ORDER BY key');
            $stmt->execute([$category]);

            $settings = [];
            foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $row) {
                $settings[$row['key']] = Settings::fromArray($row);
            }

            return $settings;
        } catch (PDOException $e) {
            error_log("MySQL Settings error in getByCategory: " . $e->getMessage());
            return [];
        }
    }

    private function ensureTable(): void
    {
        try {
            $pdo = $this->connection->getConnection();
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    `key` VARCHAR(255) NOT NULL UNIQUE,
                    `value` TEXT NOT NULL,
                    `type` VARCHAR(50) DEFAULT 'string',
                    `description` TEXT,
                    `category` VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_settings_key (`key`),
                    INDEX idx_settings_category (`category`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
        } catch (PDOException $e) {
            error_log("MySQL Settings error creating table: " . $e->getMessage());
        }
    }
}
