<?php

declare(strict_types=1);

// Start session for admin authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load environment variables from .env file
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value);
        }
    }
}

// Simple database configuration
$dbConfig = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'dbname' => $_ENV['DB_NAME'] ?? 'drxdion',
    'username' => $_ENV['DB_USER'] ?? 'root',
    'password' => $_ENV['DB_PASS'] ?? '',
    'charset' => 'utf8mb4'
];

/**
 * Simple Router for DrxDion
 */
class SimpleRouter
{
    private array $dbConfig;

    public function __construct(array $dbConfig)
    {
        $this->dbConfig = $dbConfig;
    }

    public function handle(): void
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

        // Remove query string
        $uri = strtok($uri, '?');

        // Basic routing
        if ($uri === '/' || $uri === '/index.php') {
            $this->servePublicPage();
        } elseif (str_starts_with($uri, '/api/')) {
            $this->handleApiRequest($uri, $method);
        } elseif ($uri === '/admin' || $uri === '/admin/') {
            $this->handleAdminRoot();
        } elseif (str_starts_with($uri, '/admin/')) {
            $this->handleAdminRequest($uri, $method);
        } else {
            $this->serveStaticFile($uri);
        }
    }

    private function servePublicPage()
    {
        $indexPath = __DIR__ . '/public/index.html';
        if (file_exists($indexPath)) {
            header('Content-Type: text/html; charset=utf-8');
            readfile($indexPath);
        } else {
            http_response_code(404);
            echo "Index page not found";
        }
    }

    private function handleApiRequest($uri, $method)
    {
        header('Content-Type: application/json');

        // CSRF Token endpoint
        if ($uri === '/api/csrf-token') {
            if (empty($_SESSION['csrf_token'])) {
                $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            }

            $token = $_SESSION['csrf_token'];

            // Also set as cookie for JavaScript access
            setcookie('csrf-token', $token, [
                'path' => '/',
                'httponly' => false,
                'secure' => isset($_SERVER['HTTPS']),
                'samesite' => 'Strict'
            ]);

            echo json_encode(['token' => $token]);
            return;
        }

        // Settings endpoints
        if ($uri === '/api/get-settings') {
            $this->handleGetSettings();
            return;
        }

        if ($uri === '/api/settings/update' && $method === 'POST') {
            $this->handleUpdateSettings();
            return;
        }

        // Orders API endpoints
        if (str_starts_with($uri, '/api/orders')) {
            $this->handleOrdersAPI($uri, $method);
            return;
        }

        http_response_code(404);
        echo json_encode(['error' => 'API endpoint not found', 'uri' => $uri, 'method' => $method]);
    }

    private function handleGetSettings()
    {
        try {
            // For now, return sample data. Later this will connect to the database
            $settings = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'notification_email' => '<EMAIL>',
                'site_name' => 'DrxDion',
                'timezone' => 'Europe/Istanbul'
            ];

            echo json_encode([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to load settings: ' . $e->getMessage()
            ]);
        }
    }

    private function handleUpdateSettings()
    {
        try {
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            if (empty($data)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'No data provided'
                ]);
                return;
            }

            // For now, just return success. Later this will update the database
            echo json_encode([
                'success' => true,
                'message' => 'Settings updated successfully'
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Failed to update settings: ' . $e->getMessage()
            ]);
        }
    }

    private function handleOrdersAPI($uri, $method)
    {
        header('Content-Type: application/json');
        
        // GET /api/orders - List orders from database
        if ($uri === '/api/orders' && $method === 'GET') {
            try {
                // Connect to MySQL database
                $dsn = "mysql:host={$this->dbConfig['host']};dbname={$this->dbConfig['dbname']};charset={$this->dbConfig['charset']}";
                $pdo = new PDO($dsn, $this->dbConfig['username'], $this->dbConfig['password'], [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]);
                
                // Get orders from database
                $stmt = $pdo->prepare("
                    SELECT 
                        id,
                        customer_name,
                        customer_email,
                        customer_phone,
                        product_name,
                        price,
                        total_amount,
                        shipping_cost,
                        payment_method,
                        payment_status,
                        payment_id,
                        shipping_address,
                        status,
                        created_at,
                        updated_at
                    FROM orders 
                    ORDER BY created_at DESC 
                    LIMIT 100
                ");
                
                $stmt->execute();
                $orders = $stmt->fetchAll();
                
                echo json_encode([
                    'success' => true,
                    'data' => $orders,
                    'total' => count($orders)
                ]);
                
            } catch (PDOException $e) {
                // If database fails, return sample data
                error_log("Database error: " . $e->getMessage());
                
                $sampleOrders = [
                    [
                        'id' => 'ORD-001',
                        'customer_name' => 'Ahmet Yılmaz',
                        'customer_email' => '<EMAIL>',
                        'customer_phone' => '+90 ************',
                        'product_name' => 'Premium Package',
                        'price' => 299.99,
                        'total_amount' => 319.99,
                        'shipping_cost' => 20.00,
                        'payment_method' => 'credit_card',
                        'payment_status' => 'completed',
                        'payment_id' => 'PAY-123456',
                        'shipping_address' => 'İstanbul, Türkiye',
                        'status' => 'shipped',
                        'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                        'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
                    ]
                ];
                
                echo json_encode([
                    'success' => true,
                    'data' => $sampleOrders,
                    'total' => count($sampleOrders),
                    'note' => 'Sample data - database not available'
                ]);
            }
            return;
        }

        // For other order endpoints, return simple responses
        if ($method === 'POST') {
            echo json_encode(['success' => true, 'message' => 'Order created']);
            return;
        }

        if ($method === 'PUT' || $method === 'PATCH') {
            echo json_encode(['success' => true, 'message' => 'Order updated']);
            return;
        }

        if ($method === 'DELETE') {
            echo json_encode(['success' => true, 'message' => 'Order deleted']);
            return;
        }

        http_response_code(404);
        echo json_encode(['error' => 'Orders endpoint not found']);
    }

    private function handleAdminRoot()
    {
        // Check if admin is logged in
        if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
            // Redirect to admin dashboard/orders
            header('Location: /admin/orders');
            exit;
        } else {
            // Redirect to admin login
            header('Location: /admin/login');
            exit;
        }
    }

    private function handleAdminRequest($uri, $method)
    {
        // Handle admin login POST request
        if ($uri === '/admin/login' && $method === 'POST') {
            $this->handleAdminLogin();
            return;
        }

        // Handle admin logout request (both GET and POST)
        if ($uri === '/admin/logout' && ($method === 'POST' || $method === 'GET')) {
            $this->handleAdminLogout();
            return;
        }

        // Handle admin storage endpoints
        if (str_starts_with($uri, '/admin/storage/')) {
            $this->handleAdminStorageAPI($uri, $method);
            return;
        }

        // Handle admin orders API
        if (str_starts_with($uri, '/admin/orders') && $method !== 'GET') {
            $this->handleAdminOrdersAPI($uri, $method);
            return;
        }

        // Clean up the URI - remove trailing slashes
        $uri = rtrim($uri, '/');

        // If no extension, try .html first
        $originalFile = __DIR__ . '/public' . $uri;
        $htmlFile = $originalFile . '.html';

        $file = null;

        // Try different file possibilities
        if (file_exists($originalFile) && is_file($originalFile)) {
            $file = $originalFile;
        } elseif (file_exists($htmlFile) && is_file($htmlFile)) {
            $file = $htmlFile;
        } elseif (is_dir($originalFile)) {
            // If it's a directory, look for index.html
            $indexFile = $originalFile . '/index.html';
            if (file_exists($indexFile)) {
                $file = $indexFile;
            }
        }

        if ($file && is_file($file)) {
            // Determine content type
            $ext = pathinfo($file, PATHINFO_EXTENSION);
            switch ($ext) {
                case 'html':
                    header('Content-Type: text/html; charset=UTF-8');
                    break;
                case 'css':
                    header('Content-Type: text/css');
                    break;
                case 'js':
                    header('Content-Type: application/javascript');
                    break;
            }
            readfile($file);
        } else {
            header('HTTP/1.0 404 Not Found');
            echo "Admin page not found: " . $uri;
        }
    }

    private function handleAdminLogin()
    {
        header('Content-Type: application/json');

        try {
            // Get POST data
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Invalid JSON data']);
                return;
            }

            $username = $data['username'] ?? '';
            $password = $data['password'] ?? '';

            // Simple hardcoded admin credentials for now
            if ($username === 'admin' && $password === 'admin123') {
                // Set session
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_user'] = $username;
                $_SESSION['login_time'] = time();

                echo json_encode([
                    'success' => true,
                    'message' => 'Login successful',
                    'redirect' => '/admin/orders'
                ]);
            } else {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid username or password'
                ]);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Server error: ' . $e->getMessage()
            ]);
        }
    }

    private function handleAdminLogout()
    {
        // Check if it's an AJAX request or browser navigation
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        try {
            // Destroy session
            session_destroy();

            if ($isAjax || $_SERVER['REQUEST_METHOD'] === 'POST') {
                // AJAX or POST request - return JSON
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Logout successful',
                    'redirect' => '/admin/login'
                ]);
            } else {
                // GET request - redirect directly
                header('Location: /admin/login');
                exit;
            }
        } catch (Exception $e) {
            if ($isAjax || $_SERVER['REQUEST_METHOD'] === 'POST') {
                header('Content-Type: application/json');
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Server error: ' . $e->getMessage()
                ]);
            } else {
                header('Location: /admin/login?error=logout_failed');
                exit;
            }
        }
    }

    private function handleAdminStorageAPI($uri, $method)
    {
        header('Content-Type: application/json');

        if ($uri === '/admin/storage/status') {
            try {
                // Check MySQL status
                $mysqlStatus = $this->getMySQLStatus();
                
                // Check JSON file status
                $jsonStatus = $this->getJSONStatus();
                
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'mysql' => $mysqlStatus,
                        'json' => $jsonStatus,
                        'last_sync' => $this->getLastSyncTime(),
                        'overall_health' => ($mysqlStatus['connected'] && $jsonStatus['connected']) ? 'healthy' : 'warning'
                    ]
                ]);
            } catch (\Exception $e) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Failed to get storage status: ' . $e->getMessage()
                ]);
            }
            return;
        }

        if ($uri === '/admin/storage/sync' && $method === 'POST') {
            try {
                // Simple sync simulation for now
                $result = [
                    'success' => true,
                    'synced_records' => 0,
                    'message' => 'Storage sync completed',
                    'timestamp' => date('Y-m-d H:i:s')
                ];
                
                echo json_encode($result);
            } catch (\Exception $e) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Failed to sync storage: ' . $e->getMessage()
                ]);
            }
            return;
        }

        http_response_code(404);
        echo json_encode(['error' => 'Storage endpoint not found']);
    }

    private function handleAdminOrdersAPI($uri, $method)
    {
        header('Content-Type: application/json');

        // Basic orders API responses
        if ($method === 'POST' && $uri === '/admin/orders') {
            echo json_encode(['status' => 'success', 'message' => 'Order created']);
            return;
        }

        if ($method === 'PUT' || $method === 'PATCH') {
            echo json_encode(['status' => 'success', 'message' => 'Order updated']);
            return;
        }

        if ($method === 'DELETE') {
            echo json_encode(['status' => 'success', 'message' => 'Order deleted']);
            return;
        }

        echo json_encode(['error' => 'Orders endpoint not found']);
    }

    private function getMySQLStatus(): array
    {
        try {
            $dsn = "mysql:host={$this->dbConfig['host']};dbname={$this->dbConfig['dbname']};charset={$this->dbConfig['charset']}";
            $pdo = new PDO($dsn, $this->dbConfig['username'], $this->dbConfig['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            $stmt = $pdo->query('SELECT COUNT(*) as count FROM orders');
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

            return [
                'connected' => true,
                'records' => (int) $count,
                'type' => 'mysql',
                'status' => 'healthy'
            ];
        } catch (\Exception $e) {
            return [
                'connected' => false,
                'records' => 0,
                'type' => 'mysql',
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getJSONStatus(): array
    {
        try {
            $dataDir = __DIR__ . '/data';
            $ordersFile = $dataDir . '/orders.json';
            
            $connected = file_exists($ordersFile) && is_readable($ordersFile);
            $records = 0;
            
            if ($connected) {
                $data = json_decode(file_get_contents($ordersFile), true);
                $records = is_array($data) ? count($data) : 0;
            }

            return [
                'connected' => $connected,
                'records' => $records,
                'type' => 'json',
                'status' => $connected ? 'healthy' : 'warning'
            ];
        } catch (\Exception $e) {
            return [
                'connected' => false,
                'records' => 0,
                'type' => 'json',
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getLastSyncTime(): ?string
    {
        $syncFile = __DIR__ . '/data/last_sync.txt';
        if (file_exists($syncFile)) {
            return trim(file_get_contents($syncFile));
        }
        return null;
    }

    private function serveStaticFile($uri)
    {
        $file = __DIR__ . '/public' . $uri;
        if (file_exists($file) && is_file($file)) {
            $ext = pathinfo($file, PATHINFO_EXTENSION);
            switch ($ext) {
                case 'css':
                    header('Content-Type: text/css');
                    break;
                case 'js':
                    header('Content-Type: application/javascript');
                    break;
                case 'png':
                    header('Content-Type: image/png');
                    break;
                case 'jpg':
                case 'jpeg':
                    header('Content-Type: image/jpeg');
                    break;
                case 'webp':
                    header('Content-Type: image/webp');
                    break;
                case 'ico':
                    header('Content-Type: image/x-icon');
                    break;
            }
            readfile($file);
        } else {
            header('HTTP/1.0 404 Not Found');
            echo "File not found: " . $uri;
        }
    }
}

$router = new SimpleRouter($dbConfig);
$router->handle();
?>
