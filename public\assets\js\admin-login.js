function handleLogin(e) {
	e.preventDefault();

	const username = document.getElementById("username").value;
	const password = document.getElementById("password").value;
	const errorMessage = document.getElementById("errorMessage");

	// Get CSRF token
	function getCsrfToken() {
		// Try to get from meta tag first
		const metaToken = document.querySelector('meta[name="csrf-token"]');
		if (metaToken) {
			return metaToken.getAttribute('content');
		}
		
		// Try to get from cookie
		const cookies = document.cookie.split(';');
		for (let cookie of cookies) {
			const [name, value] = cookie.trim().split('=');
			if (name === 'csrf-token') {
				return value;
			}
		}
		return '';
	}

	// Express server login
	async function tryLogin() {
		console.log("Attempting login for user:", username); // Debug log

		try {
			const csrfToken = getCsrfToken();
			console.log("CSRF Token:", csrfToken); // Debug log
			
			const headers = {
				"Content-Type": "application/json",
			};
			
			// Add CSRF token to headers
			if (csrfToken) {
				headers["X-CSRF-Token"] = csrfToken;
			}

			console.log("Trying /admin/login with headers:", headers); // Debug log

			const response = await fetch("/admin/login", {
				method: "POST",
				headers: headers,
				credentials: "include",
				body: JSON.stringify({ 
					username, 
					password
				}),
			});

			console.log("Response status:", response.status); // Debug log

			if (response.ok) {
				const data = await response.json();
				console.log("Response data:", data); // Debug log
				if (data.success) {
					window.location.href = data.redirect || "/admin/orders";
					return;
				} else {
					errorMessage.textContent =
						data.error || data.message || "Login failed";
					errorMessage.style.display = "block";
					return;
				}
			} else {
				const errorData = await response.json().catch(() => ({}));
				console.log("Error response:", errorData); // Debug log
				errorMessage.textContent =
					errorData.message ||
					"Login failed. Please check your credentials.";
				errorMessage.style.display = "block";
			}
		} catch (error) {
			console.log("Failed to connect to server:", error);
			errorMessage.textContent =
				"Unable to connect to server. Please try again.";
			errorMessage.style.display = "block";
		}
	}

	tryLogin();
}

document.addEventListener("DOMContentLoaded", function () {
	const loginForm = document.getElementById("loginForm");
	if (loginForm) {
		loginForm.addEventListener("submit", handleLogin);
	}
});
